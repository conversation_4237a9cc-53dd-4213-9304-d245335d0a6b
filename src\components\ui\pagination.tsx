import { cn } from '@/libs/utils';

import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { DOTS, usePagination } from '../../hooks/usePagination';
import { Button } from './button';

type Props = {
  onPageChange: (pageNumber: number) => void;
  totalCount: number;
  siblingCount?: number;
  currentPage: number;
  pageSize: number;
  className?: string;
  pageNameLocalStorage?: string;
};

const Pagination = (props: Props) => {
  const { onPageChange, totalCount, siblingCount = 1, currentPage, pageSize, pageNameLocalStorage } = props;

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  if (paginationRange?.length === 0) {
    return null;
  }

  const onNext = () => {
    onPageChange(currentPage + 1);
    localStorage.setItem(String(pageNameLocalStorage), String(currentPage + 1));
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
    localStorage.setItem(String(pageNameLocalStorage), String(currentPage - 1));
  };

  const lastPage = paginationRange[paginationRange.length - 1];

  return (
    <div className="flex items-center justify-center rounded-[8px] border border-white/15 font-geistMono">
      <Button
        size={'sm'}
        variant={'transparent'}
        className="flex items-center gap-1 px-4 py-2 font-semibold text-gray-800 text-sm"
        onClick={onPrevious}
        disabled={currentPage === 1}
      >
        <ChevronLeftIcon size={18} />
      </Button>
      {paginationRange.map((pageNumber, i) => {
        if (pageNumber === DOTS) {
          return (
            <div className="mb-1 text-gray-800" key={pageNumber + i}>
              {pageNumber}
            </div>
          );
        }

        return (
          <Button
            size={'sm'}
            key={pageNumber}
            variant={`${pageNumber === currentPage ? 'damask' : 'transparent'}`}
            className={cn(
              'w-[41px] rounded-none py-[6px] font-medium text-dark-100 text-sm hover:rounded-md hover:border-2 hover:border-[#FF3901] hover:bg-[#FFEB00]',
              pageNumber === currentPage ? 'rounded-md border-2 border-[#FF3901] bg-[#FFEB00] hover:bg-[#FFEB00]' : ''
            )}
            onClick={() => {
              onPageChange(pageNumber as number);
              localStorage.setItem(String(pageNameLocalStorage), String(pageNumber));
            }}
          >
            {pageNumber}
          </Button>
        );
      })}
      <Button
        size={'sm'}
        variant={'transparent'}
        className="flex items-center gap-1 px-4 py-2 font-semibold text-gray-800 text-sm"
        onClick={onNext}
        disabled={currentPage === lastPage}
      >
        <ChevronRightIcon size={18} />
      </Button>
    </div>
  );
};

export default Pagination;
