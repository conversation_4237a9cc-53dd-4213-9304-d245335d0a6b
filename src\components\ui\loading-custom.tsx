import { cn } from '@/libs/common';

const LoadingCustomIcon = ({
  className,
  bg = 'bg-white',
  classNameChild,
}: { className?: string; bg?: string; classNameChild?: string }) => {
  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-row gap-2">
        <div className={cn('h-1 w-1 animate-bounce rounded-full', bg, classNameChild)}></div>
        <div className={cn('h-1 w-1 animate-bounce rounded-full [animation-delay:-.3s]', bg, classNameChild)}></div>
        <div className={cn('h-1 w-1 animate-bounce rounded-full [animation-delay:-.5s]', bg, classNameChild)}></div>
      </div>
    </div>
  );
};
export { LoadingCustomIcon };
