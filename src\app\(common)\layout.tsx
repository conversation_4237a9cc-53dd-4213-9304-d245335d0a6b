'use client';
import { deleteCookie, getCookie } from 'cookies-next';
import { useEffect } from 'react';

type LayoutProps = Readonly<{ children: React.ReactNode }>;
const CommonLayout = ({ children }: LayoutProps) => {
  // useEffect(() => {
  //   if (!getCookie('password') && typeof window !== 'undefined') {
  //     deleteCookie('password');
  //     window.location.pathname = '/pass';
  //   }
  // }, []);

  return <div>{children}</div>;
};

export default CommonLayout;
