import Tabs from '@/components/TabsV2';
import { Show } from '@/components/utilities';
import React, { useState } from 'react';
import TopBettors from '../TopBettors/TopBettors';
import { data_tabs } from '../utils/const';
import Activity from './Activity';
import PeerChat from './PeerChat/PeerChat';

const BottomBar = () => {
  const [tab, setTab] = useState<string | number>(data_tabs[0].value);
  return (
    <section className="flex-1">
      <div className="px-4 pt-4 lg:px-6 lg:pt-6">
        <Tabs tabs={tab} setTabs={setTab} data={data_tabs} className="text-xs" />
      </div>

      <Show when={tab === data_tabs[0].value}>
        <PeerChat />
      </Show>
      <Show when={tab === data_tabs[1].value}>
        <TopBettors />
      </Show>
      <Show when={tab === data_tabs[2].value}>
        <Activity />
      </Show>
    </section>
  );
};

export default BottomBar;
