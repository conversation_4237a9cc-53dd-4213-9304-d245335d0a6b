'use client';
import { useBattleActivity, useBattleById } from '@/api/battles/queries';
import type { IParamsBattleActivity } from '@/api/battles/types';
import { Icons } from '@/assets/icons';
import NoData from '@/components/NoData';
import Pagination from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import moment from 'moment';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const Activity = () => {
  const params = useParams();
  const id = params?.id;

  const [valueAgent, setValueAgent] = useState('ALL');
  const { data: dataAgent } = useBattleById({ variables: String(id), enabled: !!id });
  const [paramsQuery, setParamsQuery] = useState<IParamsBattleActivity>({
    page: 1,
    limit: 10,
  });

  const { data, isFetching } = useBattleActivity({
    variables: { ...paramsQuery, agent_id: valueAgent === 'ALL' ? undefined : String(valueAgent), battle_id: String(id) },
    refetchOnMount: true,
    onError: onMutateError,
  });

  return (
    <VStack spacing={12} className="pt-4 md:px-4 lg:px-6 lg:pt-6">
      <Select value={valueAgent} onValueChange={(value) => setValueAgent(value)}>
        <SelectTrigger
          icon={<Icons.caretDown size={16} />}
          className={cn(
            '!border-none flex w-fit items-center gap-2 px-0 font-geistMono font-medium text-black text-sm uppercase shadow-none data-[state=open]:ring-0'
          )}
        >
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-white font-geistMono">
          <SelectItem value={'ALL'} className="text-xs">
            All
          </SelectItem>
          <SelectItem value={dataAgent?.agent1?.id || ''} className="text-xs">
            {dataAgent?.agent1?.name}
          </SelectItem>
          <SelectItem value={dataAgent?.agent2?.id || ''} className="text-xs">
            {dataAgent?.agent2?.name}
          </SelectItem>
        </SelectContent>
      </Select>
      <Show when={!isFetching}>
        <VStack spacing={0} className="">
          {data?.data?.map((i, z) => (
            <HStack key={z} spacing={12} noWrap className="h-[67px] items-start">
              <div className="mt-1 h-7 w-7 min-w-7">
                <Image src={i?.user__avatar || '/images/no-image.png'} alt="" width={32} height={32} className="h-7 w-7 rounded-md" />
              </div>
              <VStack className="font-geistMono font-medium text-dark-600 text-xs uppercase" spacing={4}>
                <p className="line-clamp-1 whitespace-nowrap text-dark-100 text-sm">
                  {i?.user__username || '-'} bet {formatNumber(Number(i?.total_bet_amount))} for {i?.agent__name}
                </p>
                <span className="whitespace-nowrap">{moment(i?.created_at).format('hh:mm A DD/MM/YYYY')}</span>
              </VStack>
            </HStack>
          ))}
        </VStack>
      </Show>
      <Show when={!isFetching && data?.data?.length !== 0 && !!data}>
        <div className="border-white/15 border-t py-2">
          <Pagination
            onPageChange={(page) => setParamsQuery({ ...paramsQuery, page })}
            totalCount={data?.pagination?.total_item || 0}
            currentPage={paramsQuery.page || 0}
            pageSize={paramsQuery.limit || 10}
          />
        </div>
      </Show>
      <NoData
        isFetching={isFetching}
        data={data?.data}
        Icon={<Icons.boltSlash />}
        Title={'No Action Yet'}
        Paragraph={
          <>
            <br /> No activity on the radar. <br />
            Once the bets fly, they’ll show up here.
          </>
        }
      />
    </VStack>
  );
};

export default Activity;
