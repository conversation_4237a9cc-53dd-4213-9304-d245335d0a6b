import request from '../axios';
import type { IListBannerHomeResponse, IParamsLeaderboard, IResponseLeaderboard } from './types';

export const getListBannerHome = async (): Promise<IListBannerHomeResponse[]> => {
  const { data } = await request({
    url: '/api/banners/list-home-page',
    method: 'GET',
  });

  return data.data;
};

export const getListLeaderboard = async (params: IParamsLeaderboard): Promise<IResponseLeaderboard> => {
  const { data } = await request({
    url: '/api/leaderboard',
    method: 'GET',
    params,
  });

  return data;
};
