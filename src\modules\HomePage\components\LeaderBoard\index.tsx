import { useListLeaderboard } from '@/api/home-page/queries';
import type { IParamsLeaderboard } from '@/api/home-page/types';
import { Icons } from '@/assets/icons';
import NoData from '@/components/NoData';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HStack, Show } from '@/components/utilities';
import { ROUTER } from '@/libs/router';
import { date_filter } from '@/modules/AgentsDetailPage/utils/const';
import { ChevronDown } from 'lucide-react';
import moment from 'moment';
import Link from 'next/link';
import { useState } from 'react';
import WrapperSectionHome from '../WrapperSectionHome';
import LeaderBoardTable from './LeaderBoardTable';

const LeaderBoard = () => {
  const [valueDate, setValueDate] = useState<string>(date_filter[0].value);
  const [paramsQuery, setParamsQuery] = useState<IParamsLeaderboard>({
    page: 1,
    limit: 10,
    sort_type: 'DESC',
    sort_key: 'total_payout',
  });
  const { data, isFetching } = useListLeaderboard({ variables: { ...paramsQuery } });

  const handleDateRangeChange = (value: string) => {
    setValueDate(value);
    const today = moment();

    let newParams = {};

    switch (value) {
      case '7 days':
        newParams = {
          ...newParams,
          start_date: today.clone().subtract(7, 'days').format('YYYY-MM-DD 00:00:00'),
          end_date: today.format('YYYY-MM-DD 23:59:59'),
        };
        break;
      case '30 days':
        newParams = {
          ...newParams,
          start_date: today.clone().subtract(30, 'days').format('YYYY-MM-DD 00:00:00'),
          end_date: today.format('YYYY-MM-DD 23:59:59'),
        };
        break;
      case 'All time':
        newParams = {
          ...newParams,
          start_date: undefined,
          end_date: undefined,
        };
        break;
      default:
        break;
    }

    setParamsQuery((prev) => ({ ...prev, ...newParams }));
  };

  return (
    <WrapperSectionHome className="!gap-12 relative">
      <HStack pos="apart">
        <H1 className="text-[#000]">Leaderboard</H1>

        <HStack spacing={4}>
          <Select key={date_filter?.[0]?.value} value={valueDate} onValueChange={handleDateRangeChange}>
            <SelectTrigger icon={<ChevronDown size={16} />}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {date_filter?.map((item: any, index) => (
                <SelectItem value={item.value} key={index}>
                  {item.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </HStack>
      </HStack>
      <Show when={!!data?.data?.length && !isFetching}>
        <div className="-z-[1] -translate-x-1/2 pointer-events-none absolute top-[80px] left-1/2 aspect-auto h-[800px] w-full bg-[url(/images/background-leaderboard.svg)] bg-center bg-cover bg-no-repeat md:w-3/4 md:scale-125"></div>
      </Show>

      <p className="h-4"></p>
      <Show when={(!!data?.data?.length && !isFetching) || isFetching}>
        <LeaderBoardTable paramsQuery={paramsQuery} setParamsQuery={setParamsQuery} data={data} isFetching={isFetching} />
      </Show>

      <Show when={!data?.data?.length && !isFetching}>
        <NoData
          Icon={<Icons.crown />}
          Title={
            <>
              The Throne Is <br /> Unclaimed
            </>
          }
          Paragraph={
            <>
              No one’s earned a place… yet. Step into <br /> the arena and make your mark.
            </>
          }
          Button={
            <Link href={ROUTER.BATTLES}>
              <Button className="h-10 w-fit px-4 py-1.5 text-black uppercase sm:px-6">View battles</Button>
            </Link>
          }
        />
      </Show>
    </WrapperSectionHome>
  );
};

export default LeaderBoard;
0;
