import H2 from '@/components/text/H2';
import { VStack } from '@/components/utilities';

type Props = {
  title: string;
  description: string;
};

const ItemListSection = (props: Props) => {
  const { title, description } = props;
  return (
    <VStack className="items-start gap-2.5 rounded-[8px] bg-[#F3F3F3] p-4 lg:p-6">
      <H2 className=" font-semibold text-[15px] uppercase leading-[150%] lg:text-base">{title}</H2>
      <p className="font-medium text-[#424242] text-[13px] leading-[150%] tracking-[-0.56px] lg:text-sm">{description}</p>
    </VStack>
  );
};

export default ItemListSection;
