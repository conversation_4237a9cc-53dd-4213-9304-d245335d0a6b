'use client';

import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import MyProfile from '@/components/user/MyProfile';
import { HStack, VStack } from '@/components/utilities';
import { useCopy } from '@/hooks/useCopy';
import { useUserLogin } from '@/hooks/useUserLogin';
import { formatNumber, shortenAddress } from '@/libs/utils';
import { customChain } from '@/libs/web3/wagmi';
import { useUserStore } from '@/stores/UserStore';
import { useQueryClient } from '@tanstack/react-query';
import { deleteCookie } from 'cookies-next';
import { Check } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { formatUnits } from 'viem';
import { useBalance, useDisconnect } from 'wagmi';

const UserInfo = () => {
  const { user, address } = useUserLogin();
  const { logout } = useUserStore();
  const { disconnect } = useDisconnect();
  const queryClient = useQueryClient();
  const [copied, copy] = useCopy();
  const router = useRouter();
  const { data: balanceData, isFetching } = useBalance({
    address,
    chainId: customChain.id,
  });

  const handleLogout = () => {
    disconnect();
    logout();
    router.refresh();
    queryClient.clear();
    deleteCookie('access_token');
    deleteCookie('refresh_token');
  };
  return (
    <div>
      <Popover>
        <PopoverTrigger asChild>
          <Button className="shadow-[0px_0px_0px_1px_#000] hover:text-[#FF3901] hover:shadow-[_0_0_0_3px_#FF3901] data-[state=open]:bg-[#FFEB00] data-[state=open]:shadow-[_0_0_0_3px_#FF3901]">
            <HStack spacing={12}>
              <Image src={user?.avatar || '/avatars/dog.png'} alt="Avatar" width={56} height={56} className="h-7 w-7 rounded-full" />
              <span className="">{user?.username || '--'}</span>
              <span className="ml-1.5 font-semibold text-[#A1A1A1]">
                {balanceData ? formatNumber(Number(formatUnits(balanceData?.value, 18))) : 0} OG
              </span>

              <Icons.chevronDown className="stroke-[#000]" />
            </HStack>
          </Button>
        </PopoverTrigger>

        <PopoverContent className="flex flex-col gap-6 p-3">
          <VStack spacing={16}>
            <HStack spacing={16}>
              <Image src={user?.avatar || '/avatars/dog.png'} alt="Avatar" width={72} height={72} className="h-14 w-14 rounded-sm" />

              <VStack spacing={4} className="text-dark-100 text-sm">
                <span className="font-jersey15 lg:text-[32px]">{user?.username || '--'}</span>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold">{address ? shortenAddress(String(address), 4) : '--'}</span>
                  <button className="hover:opacity-75" onClick={() => copy(address)}>
                    {!copied ? <Icons.copy /> : <Check className="w-6" />}
                  </button>
                </div>
              </VStack>
            </HStack>

            <HStack className="w-full" spacing={8}>
              <Button size="sm" className="border border-[#131313] shadow-none" onClick={handleLogout}>
                Logout
              </Button>

              <MyProfile>
                <Button size="sm" className="flex-1 border border-[#131313] shadow-none">
                  My Profile
                </Button>
              </MyProfile>
            </HStack>
          </VStack>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default UserInfo;
