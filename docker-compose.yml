version: '3.7'

services:
  frontend:
    build:
      dockerfile: ./docker/Dockerfile
      args:
        NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
    # Set envrionment variables based on the .env file
    env_file:
      - .env
    restart: always

  # Add more containers below (nginx, postgres, etc.)
  nginx:
    build: ./docker/nginx
    ports:
      - '8080:8080'
