export const ABI = [
  {
    inputs: [],
    stateMutability: 'nonpayable',
    type: 'constructor',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'target',
        type: 'address',
      },
    ],
    name: 'AddressEmptyCode',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'implementation',
        type: 'address',
      },
    ],
    name: 'ERC1967InvalidImplementation',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ERC1967NonPayable',
    type: 'error',
  },
  {
    inputs: [],
    name: 'FailedCall',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidInitialization',
    type: 'error',
  },
  {
    inputs: [],
    name: 'NotInitializing',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'owner',
        type: 'address',
      },
    ],
    name: 'OwnableInvalidOwner',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'account',
        type: 'address',
      },
    ],
    name: 'OwnableUnauthorizedAccount',
    type: 'error',
  },
  {
    inputs: [],
    name: 'UUPSUnauthorizedCallContext',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'bytes32',
        name: 'slot',
        type: 'bytes32',
      },
    ],
    name: 'UUPSUnsupportedProxiableUUID',
    type: 'error',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'bettor',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'chosenAgentId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'platformFeePercentage',
        type: 'uint256',
      },
    ],
    name: 'BetPlaced',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'agentAID',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'agentBID',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'platformFeePercentage',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'publicTimeStamp',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'startTimeStamp',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'sessionDuration',
        type: 'uint256',
      },
    ],
    name: 'DebateCreated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
    ],
    name: 'DebateDeleted',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
    ],
    name: 'DebateMarkedRefundable',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'winnAgentId',
        type: 'uint256',
      },
    ],
    name: 'DebateResolved',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
    ],
    name: 'DebateUnmarkedRefundable',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'agentAID',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'agentBID',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'platformFeePercentage',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'publicTimeStamp',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'startTimeStamp',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'sessionDuration',
        type: 'uint256',
      },
    ],
    name: 'DebateUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: 'uint64',
        name: 'version',
        type: 'uint64',
      },
    ],
    name: 'Initialized',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'previousOwner',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'OwnershipTransferred',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amountOfUsers',
        type: 'uint256',
      },
    ],
    name: 'RefundSuccessful',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'implementation',
        type: 'address',
      },
    ],
    name: 'Upgraded',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'bettor',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'chosenAgentId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
    ],
    name: 'UserClaimed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'debateId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'bettor',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
    ],
    name: 'UserRefunded',
    type: 'event',
  },
  {
    inputs: [],
    name: 'UPGRADE_INTERFACE_VERSION',
    outputs: [
      {
        internalType: 'string',
        name: '',
        type: 'string',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: '_mod',
        type: 'address',
      },
    ],
    name: 'addMod',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_agentAID',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_agentBID',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_platformFeePercentage',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_publicTimeStamp',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_startTimeStamp',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_sessionDuration',
        type: 'uint256',
      },
    ],
    name: 'adminCreateDebate',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'adminDeleteDebate',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'adminMarkDebateRefundable',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'adminProcessRefunds',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_startIndex',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_batchSize',
        type: 'uint256',
      },
    ],
    name: 'adminProcessRefundsBatch',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_winAgentId',
        type: 'uint256',
      },
    ],
    name: 'adminResolveDebate',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'adminUnmarkDebateRefundable',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_agentAID',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_agentBID',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_platformFeePercentage',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_publicTimeStamp',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_startTimeStamp',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_sessionDuration',
        type: 'uint256',
      },
    ],
    name: 'adminUpdateDebate',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'defaultPlatformFeePercentage',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'getAddressJoinedList',
    outputs: [
      {
        internalType: 'address[]',
        name: '',
        type: 'address[]',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'address',
        name: '_user',
        type: 'address',
      },
      {
        internalType: 'uint256',
        name: '_agentId',
        type: 'uint256',
      },
    ],
    name: 'getBetInfo',
    outputs: [
      {
        components: [
          {
            internalType: 'uint256',
            name: 'debateId',
            type: 'uint256',
          },
          {
            internalType: 'address',
            name: 'bettor',
            type: 'address',
          },
          {
            internalType: 'uint256',
            name: 'amount',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'winAmount',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'chosenAgentId',
            type: 'uint256',
          },
          {
            internalType: 'bool',
            name: 'isClaimed',
            type: 'bool',
          },
          {
            internalType: 'bool',
            name: 'isRefunded',
            type: 'bool',
          },
        ],
        internalType: 'struct AIDebate.Bet',
        name: '',
        type: 'tuple',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'getDebateInfo',
    outputs: [
      {
        components: [
          {
            internalType: 'bool',
            name: 'isResolved',
            type: 'bool',
          },
          {
            internalType: 'bool',
            name: 'isRefundable',
            type: 'bool',
          },
          {
            internalType: 'uint256',
            name: 'winAgentId',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'platformFeePercentage',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'sessionDuration',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'startTimeStamp',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'publicTimeStamp',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'agentAID',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'agentBID',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'totalAgentABetAmount',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'totalAgentBBetAmount',
            type: 'uint256',
          },
        ],
        internalType: 'struct AIDebate.Debate',
        name: '',
        type: 'tuple',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'getModList',
    outputs: [
      {
        internalType: 'address[]',
        name: '',
        type: 'address[]',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'getRefundBatchInfo',
    outputs: [
      {
        internalType: 'uint256',
        name: 'totalUsers',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: 'pendingRefunds',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: 'totalRefundAmount',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'address',
        name: '_user',
        type: 'address',
      },
      {
        internalType: 'uint256',
        name: '_agentId',
        type: 'uint256',
      },
    ],
    name: 'getUserRefundStatus',
    outputs: [
      {
        internalType: 'bool',
        name: 'isRefunded',
        type: 'bool',
      },
      {
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'address',
        name: '_user',
        type: 'address',
      },
    ],
    name: 'getUserRefundableAmount',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'getUsersRefundInfo',
    outputs: [
      {
        components: [
          {
            internalType: 'address',
            name: 'user',
            type: 'address',
          },
          {
            internalType: 'uint256',
            name: 'amountOfRefund',
            type: 'uint256',
          },
          {
            internalType: 'bool',
            name: 'refunded',
            type: 'bool',
          },
        ],
        internalType: 'struct AIDebate.UserRefundInfo[]',
        name: '',
        type: 'tuple[]',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'address',
        name: '_user',
        type: 'address',
      },
    ],
    name: 'hasUserJoined',
    outputs: [
      {
        internalType: 'bool',
        name: '',
        type: 'bool',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'initialOwner',
        type: 'address',
      },
      {
        internalType: 'address',
        name: 'initialDeployer',
        type: 'address',
      },
    ],
    name: 'initialize',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'isDebateRefundable',
    outputs: [
      {
        internalType: 'bool',
        name: '',
        type: 'bool',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'owner',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_amount',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: '_chosenAgent',
        type: 'uint256',
      },
    ],
    name: 'placeBet',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'proxiableUUID',
    outputs: [
      {
        internalType: 'bytes32',
        name: '',
        type: 'bytes32',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: '_mod',
        type: 'address',
      },
    ],
    name: 'removeMod',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'renounceOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'transferOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'newImplementation',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'data',
        type: 'bytes',
      },
    ],
    name: 'upgradeToAndCall',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'userClaim',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'userRefund',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'withdrawAll',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_amount',
        type: 'uint256',
      },
    ],
    name: 'withdrawByAmount',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '_debateId',
        type: 'uint256',
      },
    ],
    name: 'withdrawByID',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
] as const;
