import { useParentAgent } from '@/api/agent/queries';
import { Show, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import DebaterAgentItem from '@/modules/AgentsPage/components/DebaterAgentItem';
import CustomNode from './CustomNode';
import DashedStepEdge from './DashedStepEdge';
import SubStructure from './SubStructure';

const edgeTypes = {
  dashedStep: DashedStepEdge,
};
const nodeTypes = { custom: CustomNode };

const GenFirst = () => {
  const { data, isFetching } = useParentAgent({ onError: onMutateError });

  const initNodes = [
    { id: '1', type: 'custom', data: { ...data?.[0], gen: 0 }, position: { x: 275, y: 0 } },
    { id: '2', type: 'custom', data: { ...data?.[1], gen: 0 }, position: { x: 725, y: 0 } },
    { id: '3', type: 'custom', data: { cardEmpty: true, gen: 1 }, position: { x: 500, y: 520 } },
  ];

  const initEdges = [
    { id: 'e1-3', source: '1', target: '3', type: 'dashedStep' },
    { id: 'e2-3', source: '2', target: '3', type: 'dashedStep' },
  ];
  return (
    <VStack className="gap-12">
      <div>
        <p className="align-middle font-bangers font-normal text-5xl leading-[140%] tracking-[0%]">Gen 1</p>
        <p className="align-middle font-geistMono font-medium text-[#717171] text-base uppercase leading-[150%] tracking-[0%]">
          The Legacy Continues: Smarter, Sharper, Stronger
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        <Show when={isFetching}>
          {Array.from({ length: Number(6) }).map((_, index) => (
            <DebaterAgentItem loading={isFetching} key={index} />
          ))}
        </Show>

        {data?.map((item, index) => (
          <div className="flex flex-col items-center" key={index}>
            <SubStructure {...item} />

            <DebaterAgentItem key={item.id} {...item} className="w-full" />
          </div>
        ))}
      </div>
    </VStack>
  );
};

export default GenFirst;
