'use client';

import { Icons } from '@/assets/icons';
import H1 from '@/components/text/H1';
import H2 from '@/components/text/H2';
import { Button } from '@/components/ui/button';
import { VStack } from '@/components/utilities';
import { ROUTER } from '@/libs/router';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

const Error = ({ error, reset }: ErrorProps) => {
  const router = useRouter();

  useEffect(() => {
    console.error('Battle page error:', error);
  }, [error]);

  return (
    <div className="container mx-auto flex min-h-[60vh] items-center justify-center px-6 py-12">
      <VStack align="center" spacing={24} className="max-w-md text-center">
        <div className="rounded-full bg-error p-6">
          <Icons.warning className="h-12 w-12 text-error-text" />
        </div>

        <VStack align="center" spacing={16}>
          <H1
            className="font-jersey15 font-normal text-[#FF3901] uppercase leading-[normal]"
            style={{
              textShadow: '1px 2px 0px #ffeb00, 2px 4px 0px #ffeb00',
            }}
          >
            Battle Error!
          </H1>

          <H2 className="font-geistMono font-semibold text-[#131313]">Something went wrong with this battle</H2>

          <p className="font-geistMono font-medium text-[#717171] text-sm uppercase leading-[150%]">
            We encountered an unexpected error while loading this battle. This might be a temporary issue.
          </p>
        </VStack>

        <VStack spacing={12} className="w-full">
          <Button onClick={reset} className="w-full">
            Try Again
          </Button>

          <div className="flex w-full gap-3">
            <Button onClick={() => router.back()} variant="outline" className="flex-1">
              <Icons.arrowRight className="mr-2 h-4 w-4 rotate-180" />
              Go Back
            </Button>

            <Link href={ROUTER.BATTLES} className="flex-1">
              <Button variant="outline" className="w-full">
                All Battles
              </Button>
            </Link>
          </div>

          <Link href={ROUTER.HOME} className="w-full">
            <Button variant="transparent" className="w-full text-[#717171] hover:text-[#131313]">
              Return Home
            </Button>
          </Link>
        </VStack>

        {/* Error Details (Development) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-8 w-full">
            <summary className="cursor-pointer font-geistMono text-[#717171] text-xs hover:text-[#131313]">
              Error Details (Dev Only)
            </summary>
            <div className="mt-2 rounded-md bg-gray-100 p-3 text-left">
              <pre className="whitespace-pre-wrap break-words text-gray-700 text-xs">
                {error.message}
                {error.digest && `\nDigest: ${error.digest}`}
              </pre>
            </div>
          </details>
        )}
      </VStack>
    </div>
  );
};

export default Error;
