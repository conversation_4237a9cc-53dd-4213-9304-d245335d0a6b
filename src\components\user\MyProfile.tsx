'use client';

import { editAccount } from '@/api/auth/requests';
import { Icons } from '@/assets/icons';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useCopy } from '@/hooks/useCopy';
import { useUserLogin } from '@/hooks/useUserLogin';
import { type EditAccountSchema, editAccountSchema } from '@/layouts/MainLayout/libs/validators';
import { onMutateError } from '@/libs/common';
import { shortenAddress } from '@/libs/utils';
import type { FCC } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { TextField } from '../form';
import { UploadAvatarField } from '../form/UploadAvatarField';
import H2 from '../text/H2';
import { Button } from '../ui/button';
import { FormWrapper } from '../ui/form';
import { HStack, Show, VStack } from '../utilities';

interface Props {}

const MyProfile: FCC<Props> = ({ children }) => {
  const [copied, copy] = useCopy();

  const [isOpen, setIsOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const { user, refetch } = useUserLogin();
  const form = useForm<EditAccountSchema>({
    resolver: zodResolver(editAccountSchema),
    defaultValues: {
      avatar: user?.avatar,
      username: user?.username,
      wallet_address: user?.wallet_address,
    },
  });

  const { mutate, isLoading } = useMutation(editAccount);

  const handleSubmit: SubmitHandler<EditAccountSchema> = (formData) => {
    mutate(
      { avatar: formData.avatar, username: formData.username },
      {
        onSuccess: () => {
          setIsOpen(false);
          setIsEdit(false);
          refetch();
          toast.success('Edit profile successfully!');
        },
        onError: onMutateError,
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="flex w-full max-w-[340px] flex-col gap-0 rounded-[24px] px-6 sm:px-8 md:max-w-[424px]">
        <HStack pos="right">
          <button className="hover:opacity-75" onClick={() => setIsOpen(false)}>
            <Icons.X />
          </button>
        </HStack>

        <H2 className="mt-3 mb-5 text-center font-bangers font-medium text-4xl text-[#000000] lg:text-5xl">EDIT PROFILE</H2>

        <FormWrapper form={form} onSubmit={handleSubmit}>
          <VStack spacing={12} className="p-8">
            <div className="mb-6 flex justify-center">
              <UploadAvatarField
                disabled={!isEdit}
                control={form.control}
                name="avatar"
                accept={['image/png', 'image/webp', 'image/jpeg']}
              />
            </div>

            <TextField
              required
              labelClassName="text-[#717171] text-xs lg:text-xs uppercase"
              label="USERNAME"
              name="username"
              placeholder="Enter username"
              control={form.control}
              disabled={!isEdit}
              className="w-full rounded-[8px] border-none bg-[#F3F3F3] text-xs placeholder:font-geistMono focus:bg-[#FFEB0033] "
            />
            <div>
              <span className="my-1 block font-medium text-[#D0D0D0] text-xs uppercase">Wallet address</span>
              <div className="flex h-10 items-center justify-between rounded-[8px] bg-[#F3F3F3] px-4 py-2">
                <span className="font-normal text-xs opacity-40">
                  {user?.wallet_address ? shortenAddress(String(user?.wallet_address), 5) : '--'}
                </span>
                <button
                  className="hover:opacity-75"
                  onClick={(e) => {
                    e.preventDefault();
                    copy(user?.wallet_address);
                  }}
                >
                  {!copied ? <Icons.copy /> : <Check className="w-6" />}
                </button>
              </div>
            </div>

            <div className="mt-4">
              <Show when={!isEdit}>
                <Button type="button" size="md" className="w-full" onClick={() => setIsEdit(true)}>
                  <HStack spacing={8} className="font-semibold text-base">
                    Edit <Icons.edit className="h-4 w-4" />
                  </HStack>
                </Button>
              </Show>

              <Show when={isEdit}>
                <Button type="submit" className="w-full border border-dark-100 shadow-none" size="md" loading={isLoading}>
                  Save changes
                </Button>
              </Show>
            </div>
          </VStack>
        </FormWrapper>
      </DialogContent>
    </Dialog>
  );
};

export default MyProfile;
