import { motion } from 'framer-motion';
import type { LucideIcon } from 'lucide-react';
import type { FC } from 'react';

import { cn } from '@/libs/utils';
import { HStack } from './utilities';

interface Option {
  label: string;
  value: string | number;
  icon?: LucideIcon;
}
interface Props {
  data: Option[];
  onChange: (value: string | number, tab?: Option) => void;
  value: string | number;
  layoutId: string;
  className?: string;
  borderItemClassName?: string;
  activeItemClassName?: string;
  inactiveItemClassName?: string;
}

const Tabs: FC<Props> = ({
  data,
  onChange,
  value,
  layoutId,
  className,
  borderItemClassName,
  activeItemClassName = 'active relative inline-block pb-2 text-primary-700',
  inactiveItemClassName = 'text-neutral-40 relative inline-block border-transparent pb-2 hover:text-primary-700',
}) => {
  return (
    <div className={cn('border-b', className)}>
      <ul className="flex flex-wrap gap-6">
        {data.map((tab) => {
          const Icon = tab?.icon as LucideIcon;
          return (
            <li
              onClick={() => {
                onChange(tab.value, tab);
              }}
              className={cn(value === tab.value ? activeItemClassName : inactiveItemClassName, 'cursor-pointer text-center')}
              key={tab.value}
            >
              <HStack spacing={8}>
                {tab.icon && <Icon />}
                <p className="font-medium text-base"> {tab.label}</p>
              </HStack>

              {value === tab.value ? (
                <motion.div
                  layoutId={layoutId}
                  className={cn('absolute bottom-0 z-[5] h-[.125rem] w-full bg-primary-700', borderItemClassName)}
                  initial={{ width: '0' }}
                  animate={{ width: '100%' }}
                  exit={{ width: 0 }}
                />
              ) : null}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default Tabs;
