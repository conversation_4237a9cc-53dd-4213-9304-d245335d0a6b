import { createQuery } from 'react-query-kit';
import { getListBannerHome, getListLeaderboard } from './requests';
import type { IListBannerHomeResponse, IParamsLeaderboard, IResponseLeaderboard } from './types';

export const useListBannerHome = createQuery<IListBannerHomeResponse[], any>({
  queryKey: ['/api/banner-home'],
  fetcher: (params) => getListBannerHome(),
});

export const useListLeaderboard = createQuery<IResponseLeaderboard, IParamsLeaderboard>({
  queryKey: ['/api/leaderboard'],
  fetcher: (params) => getListLeaderboard(params),
});
