import { Icons } from '@/assets/icons';

export const getStatus = (status: string, label?: string) => {
  switch (status) {
    case 'ongoing':
      return (
        <>
          <div className="flex h-7 w-fit items-center justify-center rounded-[9px] bg-[#FF8101] px-3 py-2 font-geistMono font-semibold text-white text-xs transition-all duration-200 group-hover:hidden">
            <p>ONGOING</p>
          </div>
          <div className="relative hidden h-7 w-fit flex-nowrap items-center justify-center gap-3 rounded-[9px] bg-[#FF8101] px-3 py-2 font-geistMono font-semibold text-white text-xs uppercase transition-all duration-200 group-hover:flex">
            View Battle <Icons.arrowRight />
            <div className="-z-[1] absolute inset-0 top-0 flex h-7 animate-blink rounded-[9px] shadow-[_0_3px_5px_2px_#ffd9ac] transition-all [transition-duration:2000ms]"></div>
          </div>
        </>
      );
    case 'upcoming':
      return (
        <div className="flex h-7 w-fit items-center justify-center rounded-[9px] bg-[#131313] px-3 py-2 font-geistMono font-semibold text-white text-xs">
          <p>UPCOMING</p>
        </div>
      );
    case 'completed':
      return (
        <div className="flex h-7 w-fit items-center justify-center rounded-[9px] bg-[#131313] px-3 py-2 font-semibold text-green-500 text-xs">
          <p>{label} WINS</p>
        </div>
      );
    default:
      return '';
  }
};
