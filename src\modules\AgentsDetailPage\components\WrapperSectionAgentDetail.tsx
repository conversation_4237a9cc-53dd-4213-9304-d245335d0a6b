import H1 from '@/components/text/H1';
import type { ReactNode } from 'react';

type Props = {
  icon: ReactNode;
  title: string;
  children: ReactNode;
};

const WrapperSectionAgentDetail = (props: Props) => {
  const { icon, title, children } = props;
  return (
    <div>
      {icon}
      <H1 className="mt-2 mb-6 font-bangers font-normal leading-[140%] lg:text-4xl">{title}</H1>
      {children}
    </div>
  );
};

export default WrapperSectionAgentDetail;
