import type { IBattleDetail } from '@/api/battles/types';
import { Icons } from '@/assets/icons';
import { TimeBlock } from '@/components/CountdowAnimation';
import { HStack, Show, VStack } from '@/components/utilities';
import BoxModule from '@/components/wrapper/BoxModule';
import { useEffect, useState } from 'react';
import CountdownTimer from './CountdownTimer';
import { MessageBubble } from './MessageBubble';
import MessageComponent from './MessageComponent';
import { messages } from './utils/const';

const ChatChanel = ({ data, refetch }: { data: IBattleDetail | undefined; refetch?: () => void }) => {
  const [remainingTime, setRemainingTime] = useState('');
  const [minutes, setMinutes] = useState('00');
  const [seconds, setSeconds] = useState('00');
  const [isStatus, setIsStatus] = useState(data?.status);

  useEffect(() => {
    if (isStatus === 'upcoming') return;

    const interval = setInterval(() => {
      const now = new Date();
      const startTime = new Date(data?.start_at || '');
      const endTime = new Date(startTime.getTime() + (data?.duration || 0) * 60000);
      const diff = Math.max(0, endTime.getTime() - now.getTime());

      if (diff <= 0) {
        setMinutes('00');
        setSeconds('00');
        setRemainingTime('Time over');
        clearInterval(interval);
      } else {
        const min = Math.floor(diff / 60000);
        const sec = Math.floor((diff % 60000) / 1000);
        setMinutes(String(min).padStart(2, '0'));
        setSeconds(String(sec).padStart(2, '0'));
        setRemainingTime(`${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}`);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [data, isStatus]);

  useEffect(() => {
    if (data?.status) {
      setIsStatus(data?.status);
    }
  }, [data?.status]);

  return (
    <BoxModule className="bg-white/35 px-0 md:py-16">
      <VStack spacing={32}>
        <HStack noWrap pos="apart">
          <p className="whitespace-nowrap font-bangers font-normal text-4xl text-black">Live Battle</p>
          <HStack className="font-geistMono font-medium text-base text-dark-600 uppercase" noWrap>
            {remainingTime === 'Time over' ? <Icons.watchRed /> : <Icons.watch />}
            <div className={`text-base ${remainingTime === 'Time over' ? 'text-amaranth-700' : 'text-dark-600'}`}>
              {isStatus === 'upcoming' ? (
                <span className="whitespace-nowrap">
                  Duration <span className="text-dark-100">{data?.duration} Min</span>
                </span>
              ) : remainingTime === 'Time over' ? (
                <span>Time Over</span>
              ) : (
                <HStack spacing={8}>
                  <TimeBlock value={minutes} delay={100} className="text-base" />
                  <span className="font-medium text-base text-dark-100">:</span>
                  <TimeBlock value={seconds} delay={100} className="text-base" />
                </HStack>
              )}
            </div>
          </HStack>
        </HStack>
        <Show when={isStatus === 'upcoming'}>
          <VStack spacing={16} className="relative mx-auto h-[350px] w-full px-6 py-7 text-center md:h-[460px]">
            <div className="absolute inset-0 z-10 backdrop-blur-lg backdrop-brightness-105">
              <CountdownTimer targetDate={data?.start_at ?? ''} refetch={refetch} setIsStatus={setIsStatus} />
            </div>

            <div className="hidden h-[400px] w-full flex-col gap-4 md:flex">
              <MessageBubble agent={messages[0].agent} message={messages[0].content} isCurrentUser={true} />
              <MessageBubble agent={messages[1].agent} message={messages[1].content} isCurrentUser={false} />
            </div>
          </VStack>
        </Show>
        <Show when={isStatus !== 'upcoming'}>
          <MessageComponent data={data} remainingTime={remainingTime} />
        </Show>
      </VStack>
    </BoxModule>
  );
};

export default ChatChanel;
