'use client';
import { cn } from '@/libs/common';
import Web3Provider from '@/libs/web3/Provider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { CheckCircle2, Loader, X } from 'lucide-react';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ReactNode, useEffect, useState } from 'react';
import { ToastContainer, type TypeOptions } from 'react-toastify';
import 'react-toastify/ReactToastify.min.css';
import 'reactflow/dist/style.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      staleTime: 5 * 1000,
      retry: false,
      // structuralSharing
    },
  },
});

const getCustomIconToast = (type: TypeOptions, isLoading?: boolean) => {
  switch (type) {
    case 'success':
      return <CheckCircle2 width={24} height={24} className="text-[#131313]" />;
    case 'error':
      return <CheckCircle2 width={24} height={24} />;
    case 'default':
      return <Loader className="animate-spin" />;
    case 'info':
      return isLoading ? <Loader className="animate-spin" /> : <CheckCircle2 width={24} height={24} />;
    default:
      return <CheckCircle2 />;
  }
};

const CloseButton = ({
  closeToast,
  type,
}: {
  closeToast: (e: React.MouseEvent<HTMLElement>) => void;
  type: TypeOptions;
}) => {
  return (
    <span
      onClick={closeToast}
      className={cn('hover:cursor-pointer', {
        'text-[#131313]': type === 'success',
      })}
    >
      <X />
    </span>
  );
};
export interface ProvidersProps {
  children: ReactNode;
}

function Providers({ children }: ProvidersProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <NextThemesProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <Web3Provider>
        <QueryClientProvider client={queryClient}>
          {children}
          <ReactQueryDevtools buttonPosition="bottom-left" initialIsOpen={false} />

          <ToastContainer
            icon={({ type, isLoading }) => <div>{getCustomIconToast(type, isLoading)}</div>}
            autoClose={2500}
            toastClassName={(context) => {
              const { type } = context || {};
              return cn('relative flex items-start justify-between rounded-[12px] m-4 p-4 gap-4', {
                'bg-[#31FF08] text-[#131313]': type === 'success',
                'bg-[#FF3901] text-[#FFF]': type === 'error',
                'bg-[#387ad0] text-[#fff]': type === 'default',
                'bg-[#FE9800] text-[#fff]': type === 'info',
              });
            }}
            bodyClassName={() => 'text-sm font-geistMono font-medium leading-[150%] uppercase w-full flex gap-2 items-start !break-words'}
            closeButton={CloseButton}
            hideProgressBar={true}
            className={'md:!max-w-[450px] md:!w-fit'}
            draggable
            pauseOnHover
            position="bottom-right"
          />

          <ProgressBar height="2px" color="#a3683c" options={{ showSpinner: false }} shallowRouting />
        </QueryClientProvider>
      </Web3Provider>
    </NextThemesProvider>
  );
}

export default Providers;
