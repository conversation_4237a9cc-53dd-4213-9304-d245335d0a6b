'use client';
import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next-nprogress-bar';
import Image from 'next/image';
import { type FormEvent, useState } from 'react';
import { toast } from 'react-toastify';

// to do remove
const PasscodePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  async function onSubmit(event: FormEvent<HTMLFormElement>) {
    event.preventDefault();

    const formData = new FormData(event.currentTarget);
    setLoading(true);
    const response = await fetch('/api/', {
      method: 'POST',
      body: formData,
    });

    setLoading(false);

    if (response.status === 200) {
      router.push('/', { scroll: false });
      sessionStorage.setItem('isLogin', '1');
      toast.success('Passcode correctly!');
      return;
    }

    toast.error('Passcode invalid!');
  }

  return (
    <div
      className="fixed top-0 right-0 bottom-0 left-0 z-[100] h-screen bg-cover font-geistMono"
      style={{
        backgroundImage: `
            radial-gradient(50% 50% at 50% 50%, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0) 100%),
            linear-gradient(180deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 50%),
            url('/images/bg.jpg')`,
        backgroundColor: 'lightgray',
        backgroundPosition: '50%',
      }}
    >
      <div className="m-auto flex h-screen max-w-[424px] shrink-0 flex-col items-center justify-center gap-16 px-6 sm:gap-40 sm:px-0">
        <div className="absolute top-8 h-[67px] w-[155px] sm:relative sm:h-[140px] sm:w-[260px]">
          <Image src="/images/logo-header.png" alt="logo" fill sizes="100vh" className="-rotate-[5deg]" priority />
        </div>
        <h1 className="text-center font-bangers font-normal text-4xl text-white uppercase leading-[140%] sm:hidden">
          head-to-head <br /> Agent Battles
        </h1>
        <form onSubmit={onSubmit} className="flex w-full flex-col items-center gap-6">
          <Input
            variant="default"
            type="password"
            className="flex-1 gap-10 rounded-[7px] border-white bg-transparent text-center text-white placeholder:text-center placeholder:font-geistMono placeholder:text-sm placeholder:text-white focus:placeholder:invisible"
            name="password"
            placeholder="ENTER PASSCODE HERE"
            fullWidth
          />
          <Button
            className="w-full border-black bg-white font-geistMono text-base text-black hover:border-red-600 hover:bg-[#FFEB00] hover:shadow-[-2px_-3px_4px_1px_rgba(0,0,0,0.24)_inset,_0_0_0_3px_#FF3901]"
            type="submit"
            loading={loading}
          >
            CONTINUE
          </Button>
        </form>
        <div className="hidden sm:block sm:h-[100px]"></div>
        <button className="mt-8 flex items-center rounded-[7px] bg-white px-4 py-1.5 text-center font-semibold text-base uppercase leading-[150%] shadow-[-2px_-3px_2px_1px_#00000040_inset] hover:bg-slate-100 sm:hidden">
          <Icons.twitter /> <span className="ml-1 font-geistMono text-base">Follow Us</span>
        </button>
      </div>
    </div>
  );
};

export default PasscodePage;
