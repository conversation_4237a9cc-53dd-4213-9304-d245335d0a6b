import { getAllBattleForGenerateStatic, getBattleForSaleById } from '@/api/battles/requests';
import { siteConfig } from '@/config/site';

export const revalidate = 60;

export async function generateMetadata({ params }: any) {
  const { id } = await params;
  const battle = await getBattleForSaleById(id);

  return {
    title: battle?.topic,
    openGraph: {
      images: [battle?.banner_url],
    },
    twitter: {
      card: 'summary_large_image',
      title: battle?.topic,
      description: battle?.content,
      images: [battle?.banner_url],
      creator: `@${siteConfig.twitterHandle}`,
    },
  };
}

export async function generateStaticParams() {
  const battles = await getAllBattleForGenerateStatic();

  return battles.map((battle) => ({
    id: battle.id?.toString(),
    slug: battle.slug,
  }));
}

export { default } from '@/modules/BattlesDetailPage';
