type Props = {
  isFill?: boolean;
};
const SortIcon = (props: Props) => {
  const { isFill, ...rest } = props;
  return (
    <svg width={18} height={18} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.93715 3.81667L11.5753 6.1921C11.9786 6.77696 11.4607 7.50003 10.6379 7.50003H7.36217C6.53934 7.50003 6.02144 6.7774 6.42478 6.1921L8.06292 3.81667C8.47326 3.21996 9.52573 3.21996 9.93715 3.81667Z"
        stroke="#717171"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill={isFill ? '#333' : undefined}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.93715 14.1833L11.5753 11.8079C11.9786 11.223 11.4607 10.5 10.6379 10.5H7.36217C6.53934 10.5 6.02144 11.2226 6.42478 11.8079L8.06292 14.1833C8.47326 14.78 9.52573 14.78 9.93715 14.1833Z"
        stroke="#717171"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill={isFill ? '#333' : undefined}
      />
    </svg>
  );
};
export default SortIcon;
