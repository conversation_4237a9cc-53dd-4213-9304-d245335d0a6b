import { VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import type { ReactNode } from 'react';

type Props = {
  children: ReactNode;
  className?: string;
};
const WrapperSectionHome = (props: Props) => {
  const { children, className } = props;
  return (
    <VStack className={cn('mx-auto w-full max-w-[1440px] flex-1 gap-6 px-6 py-12 sm:gap-5 sm:px-16 sm:py-16', className)}>
      {children}
    </VStack>
  );
};

export default WrapperSectionHome;
