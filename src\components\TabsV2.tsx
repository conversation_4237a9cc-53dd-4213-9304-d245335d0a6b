'use client';
import { HStack } from '@/components/utilities';
import { cn } from '@/libs/utils';
import { motion } from 'framer-motion';
import React, { useId } from 'react';
import { Tooltip } from './ui/tooltip';

type Props = {
  tabs: string | number;
  setTabs: (newTab: string | number) => void;
  data: {
    label: string;
    value: string;
    disabled?: boolean;
    tooltip?: React.ReactNode;
  }[];
  className?: string;
  itemClassName?: string;
  tabClassName?: string;
};

const Tabs = ({ tabs, setTabs, data, className, itemClassName, tabClassName }: Props) => {
  const id = useId();
  return (
    <HStack
      spacing={6}
      pos="apart"
      className={cn(
        'no-scrollbar relative max-w-full overflow-x-auto whitespace-nowrap rounded-[8px] border-2 border-[#000] bg-white p-0.5',
        className
      )}
      noWrap
    >
      {data.map((item, index) => {
        const isActive = tabs === item.value;

        return (
          <div key={index} className={cn('rounded-[6px]', itemClassName)} onClick={() => !item.disabled && setTabs(item.value)}>
            <Tooltip label={item.tooltip} disabled={!item.tooltip}>
              <div
                className={cn(
                  'cursor-pointer rounded-[6px] px-3 py-1.5 text-center font-medium text-sm uppercase transition-colors',
                  isActive ? 'bg-black text-white' : 'text-[#000000] opacity-60',
                  {
                    'cursor-not-allowed text-[#A1A1A1] opacity-80': item.disabled,
                  },
                  tabClassName
                )}
              >
                {item.label}
              </div>
            </Tooltip>

            {isActive && (
              <motion.div
                layoutId={id}
                className="absolute inset-0 z-[-1] rounded-[6px] bg-secondary-400"
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
              />
            )}
          </div>
        );
      })}

      <style jsx global>{`
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .no-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </HStack>
  );
};

export default Tabs;
