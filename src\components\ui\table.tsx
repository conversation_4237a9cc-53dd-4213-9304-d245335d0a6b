import * as React from 'react';

import { cn } from '@/libs/utils';
import type { IPagination } from '@/types';

import { Icons } from '@/assets/icons';
import { ChevronDown } from 'lucide-react';
import { HStack, Show, VStack } from '../utilities';
import Pagination from './pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { SkeletonWrapper } from './skeleton-wrapper';

const Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement>>(({ className, ...props }, ref) => (
  <div className="grid w-full">
    <div className="w-full overflow-auto">
      <table ref={ref} className={cn('w-full text-sm', className)} {...props} />
    </div>
  </div>
));
Table.displayName = 'Table';

const TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <thead ref={ref} className={cn(className)} {...props} />
);
TableHeader.displayName = 'TableHeader';

const TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <tbody ref={ref} className={cn('', className)} {...props} />
);
TableBody.displayName = 'TableBody';

const TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <tfoot ref={ref} className={cn('bg-primary font-medium text-primary-foreground', className)} {...props} />
  )
);
TableFooter.displayName = 'TableFooter';

const TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(({ className, ...props }, ref) => (
  <tr ref={ref} className={cn('transition-colors data-[state=selected]:bg-muted', className)} {...props} />
));
TableRow.displayName = 'TableRow';

const TableHead = React.forwardRef<HTMLTableCellElement, React.ThHTMLAttributes<HTMLTableCellElement>>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn('h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0', className)}
    {...props}
  />
));
TableHead.displayName = 'TableHead';

const TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement>>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn('whitespace-nowrap p-4 font-medium text-[#717171] text-sm [&:has([role=checkbox])]:pr-0', className)}
    {...props}
  />
));
TableCell.displayName = 'TableCell';

const TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(
  ({ className, ...props }, ref) => <caption ref={ref} className={cn('mt-4 text-muted-foreground text-sm', className)} {...props} />
);
TableCaption.displayName = 'TableCaption';

export { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow };

export const TableEmptyData = ({ col = 4, emptyText }: { col: number; emptyText?: string }) => {
  return (
    <TableRow className="pointer-events-none">
      <TableCell colSpan={col}>
        <VStack align="center" className="min-h-24 select-none pt-2 font-semibold">
          <Icons.noDataTable />
        </VStack>
      </TableCell>
    </TableRow>
  );
};

type TableSkeletonProps = {
  loading?: boolean;
  row?: number;
  col?: number;
};
export const TableSkeleton = ({ loading = false, row = 5, col = 4 }: TableSkeletonProps) => {
  return (
    <Show when={loading}>
      {Array.from({ length: row }, (_, index) => (
        <TableRow key={index}>
          {Array.from({ length: col }, (__, index2) => (
            <TableCell key={index2} className="py-2">
              <SkeletonWrapper loading={loading} className="h-7 w-full min-w-[16px]"></SkeletonWrapper>
            </TableCell>
          ))}
        </TableRow>
      ))}
    </Show>
  );
};

export type TablePaginationProps = {
  onPageChange: (page: number) => void;
  pagination: Partial<IPagination>;
  onPageSizeChange?: (pageSize: string) => void;
  pageNameLocalStorage?: string;
};
export const TablePagination = ({ onPageSizeChange, onPageChange, pagination, pageNameLocalStorage }: TablePaginationProps) => {
  return (
    <HStack className="items-center justify-center text-white lg:justify-between">
      <HStack className="hidden lg:flex">
        <Select value={String(pagination?.limit)} onValueChange={onPageSizeChange}>
          <SelectTrigger
            className="h-9 w-fit gap-2 rounded border border-[#131313] bg-white px-3 py-2 font-geistMono font-medium text-[#131313] text-sm"
            icon={<ChevronDown size={16} />}
          >
            <span className="text-[#717171]">Show</span> <SelectValue className=" text-sm" />
          </SelectTrigger>

          <SelectContent className="font-geistMono font-medium text-sm">
            <SelectItem value="6" className="cursor-pointer">
              6
            </SelectItem>
            <SelectItem value="12" className="cursor-pointer">
              12
            </SelectItem>
            <SelectItem value="18" className="cursor-pointer">
              18
            </SelectItem>
            <SelectItem value="24" className="cursor-pointer">
              24
            </SelectItem>
          </SelectContent>
        </Select>
      </HStack>
      <HStack className="gap-10">
        {/* <div className="ml-2 hidden font-semibold text-gray-800 text-sm lg:flex">
          Page&nbsp;
          {Number(pagination?.current_page) || 0}&nbsp;of&nbsp;
          <SkeletonWrapper>{pagination?.total_page || 0}</SkeletonWrapper>
        </div> */}
        <Pagination
          onPageChange={onPageChange}
          totalCount={pagination?.total_item || 0}
          currentPage={pagination?.current_page || 0}
          pageSize={pagination?.limit || 10}
          pageNameLocalStorage={pageNameLocalStorage}
        />
      </HStack>
    </HStack>
  );
};
