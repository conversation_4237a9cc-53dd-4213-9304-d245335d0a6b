'use client';
import { useAgentById } from '@/api/agent/queries';
import type { IAttributeAgent, ICriteriaAgent } from '@/api/agent/types';
import { Icons } from '@/assets/icons';
import Tabs from '@/components/TabsV2';
import { But<PERSON> } from '@/components/ui/button';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, Show, VStack } from '@/components/utilities';
import Container from '@/components/wrapper/Container';
import { cn, onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import BattleHistory from './components/BattleHistory/BattleHistory';
import SectionAttributeAgent from './components/SectionAttributeAgent/SectionAttributeAgent';
import SectionBio from './components/SectionBio/SectionBio';
import VoteHistory from './components/VoteHistory/VoteHistory';
import { data_battle, data_tabs } from './utils/const';

const AgentsDetailPage = () => {
  const [tab, setTab] = useState<string | number>(data_tabs[0].value);
  const params = useParams();
  const searchParams = useSearchParams();
  const id = params?.id;
  const agentType = searchParams.get('type');

  const { data, isFetching } = useAgentById({
    variables: String(id),
    onError: onMutateError,
    enabled: Boolean(id),
  });

  if (!id) {
    return <div className="py-16">Loading...</div>;
  }

  const attributes = data?.attributes || [];
  const MAP_ATTRIBUTES = ['PERSONALITY', 'EVALUATION METHODS', 'STRENGTHS', 'WEAKNESSES'];
  const sortedAttributes = [
    ...MAP_ATTRIBUTES.map((key) => attributes.find((attr) => attr.key?.toUpperCase() === key)).filter(Boolean),
    ...attributes.filter((attr) => !MAP_ATTRIBUTES.includes(attr.key?.toUpperCase())),
  ].filter(Boolean) as IAttributeAgent[];

  return (
    <Container className="-mt-20 lg:-mt-28 relative z-10">
      <VStack className={cn('w-full max-w-[868px] gap-12 sm:m-auto')}>
        <VStack className="h-fit gap-8">
          <SkeletonWrapper loading={isFetching}>
            <div className="">
              <Image
                src={data?.image_url || '/images/no-image.png'}
                width={176}
                height={176}
                alt=""
                className="h-44 w-44 rounded-2xl object-cover object-top"
              />
            </div>
          </SkeletonWrapper>

          <Show when={agentType !== 'JUDGE_AGENT'}>
            <Link href={data?.social_url || ''} target="_blank">
              <Button className="rounded-md border border-[#131313] shadow-none hover:border-none">
                <Icons.twitter />
                follow on x
              </Button>
            </Link>
          </Show>

          <HStack
            className={cn('w-full flex-col items-start justify-between gap-8 md:flex-row md:gap-16', {
              'md:items-center': agentType === 'JUDGE_AGENT',
            })}
          >
            <VStack className="flex flex-1 gap-0">
              <span className="font-geistMono font-normal text-[#717171] text-sm uppercase">{data?.role}</span>
              <h3 className="font-jersey15 font-normal text-5xl uppercase">{data?.name}</h3>
              <span className="font-geistMono font-normal text-[#717171] text-sm uppercase">{data?.summary}</span>
            </VStack>
            <Show when={agentType === 'PARENT_AGENT'}>
              <HStack className="gap-6 sm:gap-16">
                <VStack className="min-w-20 items-start justify-between gap-3 md:min-w-0">
                  <Icons.sword />
                  <span className="font-geistMono font-medium text-[#717171] text-sm uppercase leading-none">Battles</span>
                  <span className="font-bold font-geistMono text-[#131313] text-[28px] uppercase leading-none">
                    {data?.debate_count || 0}
                  </span>
                </VStack>
                <VStack className="min-w-20 items-start justify-between gap-3 md:min-w-0">
                  <Icons.medal />
                  <span className="font-geistMono font-medium text-[#717171] text-sm uppercase leading-none">Wins</span>
                  <span className="font-bold font-geistMono text-[#131313] text-[28px] uppercase leading-none">{data?.win_count || 0}</span>
                </VStack>
                <VStack className="min-w-20 items-start justify-between gap-3 md:min-w-0">
                  <Icons.bullseye />
                  <span className="font-geistMono font-medium text-[#717171] text-sm uppercase leading-none">Win rate</span>
                  <span className="font-bold font-geistMono text-[#131313] text-[28px] uppercase leading-none">
                    {formatNumber((data?.rate || 0) * 100, 2)}%
                  </span>
                </VStack>
              </HStack>
            </Show>
            <Show when={agentType === 'JUDGE_AGENT'}>
              <Link href={data?.social_url || 'https://github.com/'} target="_blank">
                <Button className="flex-row items-center gap-2.5 rounded-[7px] border border-[#131313] px-3 py-1.5 shadow-none hover:border-none">
                  <Icons.github /> <span className="text-center font-medium text-sm uppercase leading-[150%]">Github</span> <Icons.link />
                </Button>
              </Link>
            </Show>
          </HStack>
        </VStack>
        <Tabs
          tabs={tab}
          setTabs={setTab}
          data={agentType === 'JUDGE_AGENT' ? data_tabs : data_battle}
          className="w-full rounded-[8px] border border-black border-solid md:w-fit"
          itemClassName="w-full md:w-auto"
        />
        <Show when={tab === data_tabs[0].value}>
          <SectionBio
            description={data?.description || ''}
            criteria={data?.criteria || ({} as ICriteriaAgent)}
            agentType={agentType || ''}
          />

          {sortedAttributes?.map((item, index) => {
            return <SectionAttributeAgent data={item.options} title={item.key} key={index} icon={<Icons.strength />} index={index} />;
          })}
        </Show>{' '}
      </VStack>

      <div className="mt-10">
        <Show when={tab === data_battle[1].value}>
          <BattleHistory />
        </Show>

        <Show when={tab === data_tabs[1].value}>
          <VoteHistory />
        </Show>
      </div>
    </Container>
  );
};

export default AgentsDetailPage;
