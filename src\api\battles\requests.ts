import request from '../axios';
import type {
  IBattleDetail,
  ICurrentUserClaim,
  IJudgeComment,
  ILiveBattleQuery,
  ILiveBattleResponse,
  IParamsBattle,
  IParamsBattleActivity,
  IResponseBattleDashboard,
  IUserBettingResponse,
} from './types';

export const getTopBettors = async (params: { battle_id: string; agent_id?: string }) => {
  const { data } = await request({
    url: `/api/battle/top-bettors/${params.battle_id}`,
    method: 'GET',
    params,
  });

  return data;
};

export const getBattlesActivity = async (params: IParamsBattleActivity) => {
  const { data } = await request({
    url: '/user-portal/top-vote/battle-activity',
    method: 'GET',
    params,
  });

  return data;
};

export const getBattleById = async (battleId: string): Promise<IBattleDetail> => {
  const { data } = await request({
    url: '/battle/' + battleId,
    method: 'GET',
  });

  return data?.data;
};
export const getBattleForSaleById = async (battleId: string): Promise<IBattleDetail> => {
  const { data } = await request({
    url: '/battle/for-sale/' + battleId,
    method: 'GET',
  });

  return data?.data;
};

export const getBattleReportById = async (battleId: string) => {
  const { data } = await request({
    url: '/battle/reports/' + battleId,
    method: 'GET',
  });

  return data?.data;
};
export const getBattleInfoById = async (battleId: string) => {
  const { data } = await request({
    url: '/battle/info/' + battleId,
    method: 'GET',
  });

  return data?.data;
};

export const getListBattles = async (params: IParamsBattle): Promise<IResponseBattleDashboard> => {
  const { data } = await request({
    url: '/battles/list-public',
    method: 'GET',
    params,
  });

  return data;
};

export const getListLiveBattle = async (params?: Partial<ILiveBattleQuery>): Promise<ILiveBattleResponse> => {
  const { data } = await request({
    url: '/api/live-battle/' + params?.battle_id,
    method: 'GET',
    params,
  });

  return data;
};

export const getCurrentUserClaim = async (battleId: string): Promise<ICurrentUserClaim> => {
  const { data } = await request({
    url: '/battle/current-user-claim/' + battleId,
    method: 'GET',
  });

  return data?.data;
};

export const getUserBetting = async ({ battle_id, params }: { params?: any; battle_id: string }): Promise<IUserBettingResponse> => {
  const { data } = await request({
    url: '/api/battle/user-betting/' + battle_id,
    method: 'GET',
    params,
  });

  return data;
};

export const getJudgeComment = async (battle_id: string): Promise<IJudgeComment[]> => {
  const { data } = await request({
    url: '/api/battles/get-judge-comment/' + battle_id,
    method: 'GET',
  });

  return data?.data;
};

export const getYourBets = async (battle_id: string): Promise<any[]> => {
  const { data } = await request({
    url: '/api/battles/your-bets/' + battle_id,
    method: 'GET',
  });

  return data?.data;
};

export const getAllBattleForGenerateStatic = async (): Promise<IBattleDetail[]> => {
  const { data } = await request({
    url: '/api/battles/all-for-fe-generate-static',
    method: 'GET',
  });

  return data?.data;
};
