{"name": "quant-trading", "version": "0.1.0", "private": true, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "***********************:var-ai/quant-trading/var-quant-ui.git"}, "scripts": {"dev": "yarn clean && next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome lint .", "lint:fix": "biome lint . --write", "format:check": "biome format . ", "format:fix": "biome format .  --write", "check-types": "tsc --noEmit", "postinstall": "husky", "test": "jest", "test:watch": "jest --watch", "clean": "rimraf .next out"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["yarn lint:fix", "yarn format:fix"]}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@mantine/core": "^7.15.2", "@mantine/hooks": "^7.15.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@react-input/mask": "^2.0.4", "@react-oauth/google": "^0.12.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^4.29.25", "auto-zustand-selectors-hook": "^2.0.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "ethers": "^6.13.5", "framer-motion": "^11.11.10", "html-to-image": "^1.11.13", "input-otp": "^1.2.5", "lucide-react": "^0.468.0", "moment": "^2.30.1", "next": "15.1.4", "next-nprogress-bar": "^2.3.14", "next-themes": "^0.4.4", "npm": "^11.4.1", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.53.1", "react-intersection-observer": "^9.14.1", "react-query-kit": "^3.3.1", "react-select": "^5.8.2", "react-toastify": "^10.0.6", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "reactflow": "^11.11.4", "recharts": "^2.15.0", "slugify": "^1.6.6", "socket.io-client": "^4.8.1", "swiper": "^11.2.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "twitter-api-v2": "^1.23.2", "typewriter-effect": "^2.21.0", "viem": "2.x", "wagmi": "^2.14.11", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query-devtools": "^5.59.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "@types/react-window": "^1.8.8", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "5.8.3"}, "resolutions": {"@types/react": "19.0.2", "@types/react-dom": "19.0.2"}}