import type { IAgent } from '@/api/agent/types';
import { Icons } from '@/assets/icons';
import { HStack, VStack } from '@/components/utilities';
import Image from 'next/image';
type Props = {
  loading?: boolean;
} & Partial<IAgent>;

const SubStructure = ({ id, loading, name, image_url, summary, debate_count, win_count, rate, agent_type, role }: Props) => {
  return (
    <VStack className="w-fit items-center gap-0">
      <HStack className="w-fit justify-between">
        <Image src={image_url || '/avatars/no-image.png'} alt="Avatar" width={48} height={48} className="h-12 w-12 rounded-[10px]" />
        <span className="h-[18px] w-[18px]"></span>
        <Image src={image_url || '/avatars/no-image.png'} alt="Avatar" width={48} height={48} className="h-12 w-12 rounded-[10px]" />
      </HStack>
      <div>
        <Icons.structure />
      </div>
    </VStack>
  );
};

export default SubStructure;
