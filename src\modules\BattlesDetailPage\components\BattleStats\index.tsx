import type { IAgent } from '@/api/agent/types';
import { useBattleById, useBattleInfoById } from '@/api/battles/queries';
import type { IAgentReport } from '@/api/battles/types';
import { Show } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import BattleAvatar from './BattleAvatar';
import BattleBottom from './BattleBottom';

const BattleStats = () => {
  const { id } = useParams();
  const { data, isFetching } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });
  const { data: dataReport } = useBattleInfoById({ variables: String(id), enabled: Boolean(id), onError: onMutateError });

  const agent1 = data?.agent1;
  const agent2 = data?.agent2;

  const getAgentData = (agent?: IAgent): IAgentReport | undefined => {
    if (agent?.id === dataReport?.agent1_data?.agent_id) {
      return dataReport?.agent1_data;
    } else if (agent?.id === dataReport?.agent2_data?.agent_id) {
      return dataReport?.agent2_data;
    }
    return undefined;
  };

  const agent1Report = getAgentData(agent1);
  const agent2Report = getAgentData(agent2);
  return (
    <div className="relative mt-10 aspect-[935/600] w-full overflow-hidden rounded-[36px] p-6">
      <Show when={!isFetching}>
        <Image
          priority
          src={
            data?.is_resolved && data?.winner_id
              ? data?.banner_debate_end_url || '/images/battle-end.png'
              : data?.banner_url || '/images/battle-active.png'
          }
          alt=""
          width={935}
          height={600}
          className="absolute inset-0 h-auto w-full"
        />
      </Show>
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(50%_50%_at_50%_50%,rgba(0,0,0,0)_0%,rgba(0,0,0,0.4)_100%)]" />

      <div className="absolute top-[10%] right-0 left-0 flex items-center justify-center gap-12 lg:top-[12%] 2xl:top-[20%]">
        <BattleAvatar
          imageSrc={data?.agent1?.image_url || ''}
          totalBets={formatNumber(Number(agent1Report?.total_bet), Number(agent1Report?.total_bet) >= 100 ? 2 : 5)}
          agentId={data?.agent1?.id}
          battles={agent1Report?.count_bet || 0}
          name={data?.agent1?.name || ''}
          result={data?.is_resolved && data?.winner_id ? (data?.agent1?.id === data?.winner_id ? 'win' : 'lose') : undefined}
        />
        <Image src="/images/vss.png" alt="logo" width={50} height={50} className="h-18 w-18" />
        <BattleAvatar
          imageSrc={data?.agent2?.image_url || ''}
          totalBets={formatNumber(Number(agent2Report?.total_bet), Number(agent2Report?.total_bet) >= 100 ? 2 : 5)}
          battles={agent2Report?.count_bet || 0}
          name={data?.agent2?.name || ''}
          agentId={data?.agent2?.id}
          result={data?.is_resolved && data?.winner_id ? (data?.agent2?.id === data?.winner_id ? 'win' : 'lose') : undefined}
        />
      </div>

      <BattleBottom data={data} stats={dataReport?.total} />
    </div>
  );
};

export default BattleStats;
