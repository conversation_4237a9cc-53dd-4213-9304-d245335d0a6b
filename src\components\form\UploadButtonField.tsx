'use client';

import type { MIME_TYPE } from '@/libs/mime';
import { cn } from '@/libs/utils';
import React, { useRef } from 'react';
import type { Control, FieldPath, FieldPathValue, FieldValues } from 'react-hook-form';

import { Button, type ButtonProps } from '../ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '../ui/form';
import { HStack } from '../utilities';

interface Props<T extends FieldValues = FieldValues> extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'accept'> {
  control: Control<T>;
  name: FieldPath<T>;
  defaultValue?: FieldPathValue<T, FieldPath<T>>;
  fullWidth?: boolean;
  readonly?: boolean;
  accept?: MIME_TYPE[];
  loading?: boolean;
  btnProps?: ButtonProps;
}

const UploadButtonField = <T extends FieldValues>({
  accept = [],
  control,
  name,
  defaultValue,
  btnProps,
  loading,
  className,
  readonly,
  ...props
}: Props<T>) => {
  const ref = useRef<React.ElementRef<'input'>>(null);

  return (
    <FormField
      defaultValue={defaultValue}
      control={control}
      name={name}
      render={({ field: { onChange, value } }) => {
        const file = value as File;
        return (
          <FormItem>
            <Button
              disabled={props.disabled}
              className={cn('', className)}
              onClick={() => {
                if (ref.current) {
                  ref.current.value = '';
                }
                ref.current?.click();
              }}
              {...btnProps}
            >
              <HStack spacing={12} className="hover:opacity-75">
                {/* <Icons.upload className='text-grey-600' /> */}
                <div className="text-grey-600">Choose File</div>
              </HStack>
            </Button>
            <FormControl>
              <input
                hidden
                accept={accept.length === 0 ? undefined : accept.join(', ')}
                type="file"
                onChange={(e) => {
                  onChange(Array.from(e.target.files as any));
                  e.target.files = null;
                }}
                multiple
                {...props}
                ref={ref}
              />
            </FormControl>

            <FormMessage className="mt-1 text-xs" />
          </FormItem>
        );
      }}
    />
  );
};

export { UploadButtonField };
