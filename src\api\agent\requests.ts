import request from '../axios';
import type { IParamsBattleChart, IParamsBattleHistory } from './types';

export const getJudgeAgent = async () => {
  const { data } = await request({
    url: '/user-portal/judge-agent?limit=5',
    method: 'GET',
  });

  return data?.data;
};

export const getParentAgent = async () => {
  const { data } = await request({
    url: '/user-portal/parent-agent?limit=5',
    method: 'GET',
  });

  return data?.data;
};

export const getAgentById = async (agentId: string) => {
  const { data } = await request({
    url: '/api/agent/' + agentId,
    method: 'GET',
  });

  return data?.data;
};

export const getDetailBattleAmountChart = async (params: IParamsBattleChart) => {
  const { data } = await request({
    url: '/user-portal/battle-amount-chart',
    method: 'GET',
    params,
  });

  return data;
};

export const getBattleParentHistory = async (params: IParamsBattleHistory) => {
  const { data } = await request({
    url: `/api/agent/battle-parent-history/${params.agent_id}`,
    method: 'GET',
    params,
  });

  return data;
};

export const getJudgeHistory = async (params: IParamsBattleHistory) => {
  const { data } = await request({
    url: `/api/agent/battle-judge-history/${params.agent_id}`,
    method: 'GET',
    params,
  });

  return data;
};
