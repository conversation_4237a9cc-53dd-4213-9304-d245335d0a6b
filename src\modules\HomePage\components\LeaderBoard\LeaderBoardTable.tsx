'use client';
import type { ICurrentUserRankInfo, IParamsLeaderboard, IResponseLeaderboard } from '@/api/home-page/types';
import SortIcon from '@/components/icons/SortIcon';
import { Avatar } from '@/components/ui/avatar-table';
import { Table, TableBody, TableCell, TableEmptyData, TableHeader, TableRow, TableSkeleton } from '@/components/ui/table';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import React, { Fragment } from 'react';
import Stage from './Stage';
import { headersTable } from './utils/const';

const isSorting = ({ key, sort_key, sort_type }: { key: string; sort_type: string; sort_key: string }) => {
  if (sort_type === 'DESC' && key === sort_key) {
    return true;
  }

  return false;
};

const TableRowCurrentRank = ({ item }: { item: ICurrentUserRankInfo | undefined }) => {
  if (!item) {
    return null;
  }
  return (
    <TableRow key={item.rank} className="overflow-hidden rounded-[8px] bg-[#FFEB00]">
      {headersTable?.map((column, _index) => {
        return (
          <TableCell
            key={column.key}
            className={cn(
              {
                'font-semibold': _index === 0,
                'rounded-tl-[8px] rounded-bl-[8px]': _index === 0,
                'rounded-tr-[8px] rounded-br-[8px]': _index === headersTable.length - 1,
              },
              column?.className,
              'text-[#FF3901]'
            )}
          >
            {column.key === 'index' ? (
              <span>{item.rank}</span>
            ) : column.key === 'total_bet_amount' || column.key === 'total_payout' ? (
              <span>{Number(item?.[column.key]?.toFixed(5)?.toString() || '0') ?? '-'}</span>
            ) : (
              <HStack spacing={4} className="flex items-center gap-2" noWrap>
                <Show when={column.isAvatar}>
                  <Avatar src={item?.avatar ?? '/images/profile.jpg'} className="h-6 w-6 rounded-full" />
                </Show>
                {(item as any)[column.key] ?? '-'}{' '}
              </HStack>
            )}
          </TableCell>
        );
      })}
    </TableRow>
  );
};

const LeaderBoardTable = ({
  paramsQuery,
  setParamsQuery,
  data,
  isFetching,
}: {
  paramsQuery: IParamsLeaderboard;
  setParamsQuery: React.Dispatch<React.SetStateAction<IParamsLeaderboard>>;
  data: IResponseLeaderboard | undefined;
  isFetching: boolean;
}) => {
  const userRankInfo: ICurrentUserRankInfo | undefined = data?.current_user_data;

  return (
    <VStack className="gap-8 sm:px-12">
      <Stage rank={data?.data || []} rankUser={userRankInfo || ({} as ICurrentUserRankInfo)} isLoading={isFetching} />
      <div className="min-h-[680px] overflow-auto sm:p-6 sm:px-10">
        <Table>
          <TableHeader>
            <tr className="w-full items-center rounded-lg border-transparent border-t font-geist">
              {headersTable.map((column) => {
                return (
                  <TableCell
                    className={cn('py-3 align-middle font-semibold text-sm uppercase leading-4 tracking-[0px]', column.className)}
                    key={column.key}
                  >
                    <HStack
                      spacing={8}
                      noWrap
                      className={cn(column?.className, {
                        'text-[#131313]': column.isSort && paramsQuery.sort_key === column.key,
                      })}
                    >
                      <Tooltip
                        label={column.contentInfo}
                        disabled={!column?.contentInfo}
                        className={cn(
                          'rounded-[8px] bg-[#0427FA] align-middle font-geistMono font-medium text-sm text-white normal-case leading-[150%] tracking-[-3%]'
                        )}
                      >
                        <span>{column.name}</span>
                      </Tooltip>

                      <Show when={column.isSort}>
                        <div
                          className="cursor-pointer"
                          onClick={() => {
                            let sortType: 'DESC' | undefined = undefined;
                            if (paramsQuery.sort_key === column.key && paramsQuery?.sort_type === 'DESC') {
                              sortType = undefined;
                            } else {
                              sortType = 'DESC';
                            }

                            setParamsQuery((prev) => ({
                              ...prev,
                              sort_key: sortType ? column.key : 'total_payout',
                              sort_type: sortType ? sortType : 'DESC',
                            }));
                          }}
                        >
                          <SortIcon
                            isFill={isSorting({
                              key: column.key,
                              sort_key: paramsQuery?.sort_key || '',
                              sort_type: paramsQuery?.sort_type || '',
                            })}
                          />
                          <div className="hiddenHover absolute hidden max-w-[200px] whitespace-pre-wrap rounded-md border text-xs shadow">
                            {column.contentInfo}
                          </div>
                        </div>
                      </Show>
                    </HStack>
                  </TableCell>
                );
              })}
            </tr>
          </TableHeader>

          <TableBody>
            <Show when={!isFetching}>
              {data?.data?.map((item: any, index: number) => (
                <Fragment key={index}>
                  <Show when={Number(userRankInfo?.rank || 0) === index + 1}>
                    <TableRowCurrentRank item={userRankInfo} />
                  </Show>

                  <Show when={Number(userRankInfo?.rank || 0) !== index + 1}>
                    <TableRow key={index} className="border-[#D0D0D0] border-b">
                      {headersTable?.map((column, _index) => {
                        return (
                          <TableCell
                            key={column.key}
                            className={cn(
                              'font-geistMono text-[#000000]',
                              {
                                'flex items-center gap-2 font-semibold': _index === 0,
                                'text-left': _index !== 0,
                              },
                              column?.className
                            )}
                          >
                            {column.key === 'index' ? (
                              <p>{index + 1}</p>
                            ) : column.key === 'total_bet_amount' || column.key === 'total_payout' ? (
                              <span>{Number(item?.[column.key]?.toFixed(5)?.toString()) ?? '-'}</span>
                            ) : (
                              <HStack spacing={4} className="flex items-center gap-2" noWrap>
                                <Show when={column.isAvatar}>
                                  <Avatar src={item?.avatar ?? '/images/profile.jpg'} className="h-6 w-6 rounded-full" />
                                </Show>
                                <span
                                  className={cn({
                                    truncate: column.isAvatar,
                                  })}
                                >
                                  {item[column.key] ?? '-'}{' '}
                                </span>
                              </HStack>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  </Show>
                </Fragment>
              ))}

              <Show when={Number(userRankInfo?.rank || 0) > 10}>
                <TableRowCurrentRank item={userRankInfo} />
              </Show>
            </Show>

            <TableSkeleton loading={isFetching} col={headersTable.length} row={12} />

            <Show when={!isFetching && (!data?.data || data?.data?.length === 0)}>
              <TableEmptyData col={headersTable.length} emptyText="(Coming Soon...)" />
            </Show>
          </TableBody>
        </Table>
      </div>
    </VStack>
  );
};

export default LeaderBoardTable;
