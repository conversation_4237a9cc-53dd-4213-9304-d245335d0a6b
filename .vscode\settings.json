{"typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit", "quickfix.biome": "explicit", "source.sortImports": "explicit"}, "tailwindCSS.includeLanguages": {"javascript": "javascript", "javascriptreact": "javascriptreact", "typescript": "typescript", "typescriptreact": "typescriptreact", "html": "HTML"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["tv\\((([^()]*|\\([^()]*\\))*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "html.format.wrapAttributes": "auto", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "cSpell.words": ["tailwindcss"]}