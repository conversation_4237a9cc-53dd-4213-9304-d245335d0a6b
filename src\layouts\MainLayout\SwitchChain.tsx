import H2 from '@/components/text/H2';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { VStack } from '@/components/utilities';
import { useUserStore } from '@/stores/UserStore';
import { useQueryClient } from '@tanstack/react-query';
import { deleteCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'react-toastify';
import { useDisconnect, useSwitchChain } from 'wagmi';
interface Props {
  openSwitchChainDialog: boolean;
  setOpenSwitchChainDialog: React.Dispatch<React.SetStateAction<boolean>>;
}
const SwitchChain = (props: Props) => {
  const { openSwitchChainDialog, setOpenSwitchChainDialog } = props;
  const { chains, switchChain } = useSwitchChain();
  const { logout } = useUserStore();
  const { disconnect } = useDisconnect();
  const queryClient = useQueryClient();
  const router = useRouter();

  const handleDisconnect = async () => {
    await disconnect();
    await logout();
    await router.refresh();
    await queryClient.clear();
    deleteCookie('access_token');
    deleteCookie('refresh_token');
    setOpenSwitchChainDialog(false);
  };

  return (
    <Dialog open={openSwitchChainDialog}>
      <DialogContent className="max-w-sm rounded-[24px] p-6 text-center">
        <H2 className="mb-4 font-bold text-xl">Wrong Network!</H2>
        <p className="mb-6">Please switch to the supported network to continue using this app.</p>
        <VStack spacing={12}>
          <Button
            onClick={async () => {
              try {
                await switchChain({ chainId: chains[0].id });
                setOpenSwitchChainDialog(false);
              } catch (err) {
                toast.error('Failed to switch network');
              }
            }}
            className="w-full"
          >
            Switch to {chains[0].name}
          </Button>
          <Button onClick={handleDisconnect} variant="outline" className="w-full">
            Disconnect
          </Button>
        </VStack>
      </DialogContent>
    </Dialog>
  );
};

export default SwitchChain;
