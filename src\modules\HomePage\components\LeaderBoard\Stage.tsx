import type { ICurrentUserRankInfo, IDataLeaderboard } from '@/api/home-page/types';
import { Icons } from '@/assets/icons';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import Image from 'next/image';

type Props = {
  rank: IDataLeaderboard[];
  rankUser: ICurrentUserRankInfo;
  isLoading: boolean;
};

const Stage = (props: Props) => {
  const { rank, rankUser, isLoading } = props;
  return (
    <div className="flex flex-col items-center self-stretch py-0 lg:px-[132px]">
      <div className="grid grid-cols-[1fr_1fr_1fr] items-end justify-center gap-4">
        <Show when={!!rank?.[1]?.username}>
          <VStack className="flex items-center justify-between gap-[70px] lg:flex-col lg:gap-[76px]">
            <VStack className="items-center gap-4 text-center">
              <SkeletonWrapper loading={isLoading}>
                <Image
                  src={rank?.[1]?.avatar || '/images/anonymous.jpeg'}
                  width={80}
                  height={80}
                  alt="rank second"
                  className="h-[80px] w-[80px] rounded-xl border border-white/35 object-cover"
                />
              </SkeletonWrapper>

              <span className="font-jersey15 text-2xl uppercase leading-[normal] lg:text-5xl">
                {Number(rankUser?.rank || 0) === 2 ? 'you' : rank?.[1]?.username || ''}
              </span>
            </VStack>
            <div className="w-[100px] lg:w-[263px]">
              <Icons.secondStage />
              <div
                className="flex h-[136px] w-full shrink-0 items-center justify-center self-end font-bangers text-5xl tracking-[8.32px] shadow-[0px_2px_0px_0px_rgba(255,255,255,0.20)_inset] lg:h-[203px] lg:text-[64px]"
                style={{
                  background: `linear-gradient(180deg, #d9d9d9 0%, rgba(255, 255, 255, 0) 100%)`,
                }}
              >
                <Show when={!isLoading}>
                  <p>
                    2<span className="align-super text-[0.65em] leading-[58px]">nd</span>
                  </p>
                </Show>
              </div>
            </div>
          </VStack>
        </Show>

        <Show when={!!rank?.[0]?.username}>
          <VStack className="flex flex-col items-center justify-between gap-5 lg:gap-10">
            <VStack className="gap-4 text-center ">
              <SkeletonWrapper loading={isLoading}>
                <Image
                  src={rank?.[0]?.avatar || '/images/anonymous.jpeg'}
                  width={120}
                  height={120}
                  alt="rank first"
                  className="h-[120px] w-[120px] rounded-xl border border-white/35 object-cover"
                />
              </SkeletonWrapper>

              <span className="font-jersey15 text-2xl uppercase leading-[normal] lg:text-5xl">
                {Number(rankUser?.rank || 0) === 1 ? 'you' : rank?.[0]?.username || ''}
              </span>
            </VStack>
            <div className="w-[139px] lg:w-[311px]">
              <Icons.firstStage
                className={cn('fill-[#FF7501] text-[#FF7501]', {
                  'fill-[#D9D9D9] text-[#D9D9D9]': isLoading,
                })}
              />

              <HStack
                className="h-[185px] w-full shrink-0 items-center justify-center font-bangers text-[#FFF] text-[80px] tracking-[10px] shadow-[0px_2px_0px_0px_rgba(255,255,255,0.20)_inset] lg:h-[243px] lg:text-[120px] lg:tracking-[15px]"
                style={{
                  background: `linear-gradient(180deg, ${isLoading ? '#D9D9D9' : '#ff7501'} 0%, rgba(255, 255, 255, 0) 100%)`,
                }}
              >
                <Show when={!isLoading}>
                  <p>
                    1<span className="align-super text-[0.65em] leading-[58px]">st</span>
                  </p>
                </Show>
              </HStack>
            </div>
          </VStack>
        </Show>

        <Show when={!!rank?.[2]?.username}>
          <VStack className="flex flex-col items-center gap-[60px] lg:gap-[52px]">
            <VStack className="items-center gap-4 text-center">
              <SkeletonWrapper loading={isLoading}>
                <Image
                  src={rank?.[2]?.avatar || '/images/anonymous.jpeg'}
                  width={80}
                  height={80}
                  alt="rank third"
                  className="h-[80px] w-[80px] rounded-xl border border-white/35 object-cover"
                />
              </SkeletonWrapper>

              <span className="font-jersey15 font-normal text-2xl uppercase leading-[normal] lg:text-5xl">
                {Number(rankUser?.rank || 0) === 3 ? 'you' : rank?.[2]?.username || ''}
              </span>
            </VStack>

            <div className="w-[100px] lg:w-[262px]">
              <Icons.thirdStage />
              <div
                className="flex h-[126px] w-full items-center justify-center self-end font-bangers text-5xl tracking-[6px] shadow-[0px_2px_0px_0px_rgba(255,255,255,0.20)_inset] lg:h-[186px] lg:text-[64px] lg:tracking-[8.32px]"
                style={{
                  background: `linear-gradient(180deg, #d9d9d9 0%, rgba(255, 255, 255, 0) 100%)`,
                }}
              >
                <Show when={!isLoading}>
                  <p>
                    3<span className="align-super text-[0.65em] leading-[58px]">rd</span>
                  </p>
                </Show>
              </div>
            </div>
          </VStack>
        </Show>
      </div>
    </div>
  );
};

export default Stage;
