'use client';

import { useBattleById, useBattleReportById, useYourBets } from '@/api/battles/queries';
import Tabs from '@/components/TabsV2';
import H2 from '@/components/text/H2';
import H4 from '@/components/text/H4';
import { Button } from '@/components/ui/button';
import { HStack, Show, VStack } from '@/components/utilities';
import useCheckTimeOverBattle from '@/hooks/useCheckTimeOverBattle';
import { useUserLogin } from '@/hooks/useUserLogin';
import { onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import { useParams } from 'next/navigation';
import React, { useMemo, useState } from 'react';
import ButtonShare from '../ButtonShare';
import BettingClosesIn from '../FlowChannel/BettingClosesIn';
import ButtonClaim from '../FlowChannel/ButtonClaim';
import BetInfoItem from './BetInfoItem';

type Props = {
  onButtonClick?: () => void;
};
const YourBets = ({ onButtonClick }: Props) => {
  const [tab, setTab] = useState<string | number>('');

  const { id } = useParams();
  const { isLoggedIn } = useUserLogin();

  const { data: yourBets } = useYourBets({ variables: String(id), enabled: Boolean(id && isLoggedIn), onError: onMutateError });
  const { data: battle, isFetching } = useBattleById({
    variables: String(id),
    enabled: !!id,
    onError: onMutateError,
  });
  const { data: battleInfo } = useBattleReportById({ variables: String(id), enabled: !!id, onError: onMutateError });

  const { isBattleStillActive, isBattleStillBet } = useCheckTimeOverBattle({
    duration: battle?.duration,
    start_at: battle?.start_at,
  });

  const betWins = useMemo(() => {
    if (yourBets && battle?.status === 'completed' && battle?.winner_id) {
      return yourBets?.filter((item) => item.agent_id === battle?.winner_id);
    }
    return [];
  }, [battle?.winner_id, battle?.status]);

  const betLose = useMemo(() => {
    if (yourBets && battle?.status === 'completed' && battle?.winner_id) {
      return yourBets?.filter((item) => item.agent_id !== battle?.winner_id);
    }
    return [];
  }, [battle?.winner_id, battle?.status]);

  const totalPayout = useMemo(() => {
    if (yourBets && betWins?.length > 0 && battle?.status === 'completed' && battle?.winner_id) {
      return betWins?.reduce((acc, item) => acc + item.potential_payout, 0);
    }
    return 0;
  }, [battle?.winner_id, battle?.status, betWins]);

  const DATA_AGENT_TABS = useMemo(() => {
    if (!battle) return [];
    setTab(String(battle.agent1.id));

    return [
      {
        label: battle.agent1.name,
        value: String(battle.agent1.id),
        volume: Number(battleInfo?.agent1_data?.total_bet),
        current_bet: Number(battleInfo?.agent1_data?.current_bet),
        potentialPayout: battleInfo?.agent1_data?.potential_payout,
        potentialPayoutRatio: battleInfo?.agent1_data?.potential_payout_ratio,
      },
      {
        label: battle.agent2.name,
        value: String(battle.agent2.id),
        volume: Number(battleInfo?.agent2_data?.total_bet),
        current_bet: Number(battleInfo?.agent2_data?.current_bet),
        potentialPayout: battleInfo?.agent2_data?.potential_payout,
        potentialPayoutRatio: battleInfo?.agent2_data?.potential_payout_ratio,
      },
    ];
  }, [battle, battleInfo]);

  const agentSelect = DATA_AGENT_TABS?.find((x) => x.value === tab);
  const agentWin = DATA_AGENT_TABS?.find((x) => x.value === String(battle?.winner_id));

  return (
    <div className="mt-6">
      <BettingClosesIn />

      <div className="max-h-[450px] overflow-y-auto pr-1">
        <Show when={Boolean(!isBattleStillActive && battle?.is_resolved)}>
          <>
            <H2 className="mb-5 text-center font-je font-jersey15 text-3xl text-[#FF3901] [text-shadow:_1px_2px_0px_#FFEB00,_2px_4px_0px_#FFEB00] lg:text-5xl">
              {betWins?.length > 0 ? 'You Win' : 'next time!'}
            </H2>

            {/* You win */}
            <Show when={betWins?.length > 0}>
              <VStack className="" spacing={12}>
                <H4 className="font-medium text-dark-100 lg:text-sm">Won</H4>

                {betWins?.map((item, index) => (
                  <BetInfoItem key={item.id} {...item} />
                ))}
              </VStack>
            </Show>

            {/* You Lose */}
            <Show when={betLose?.length > 0}>
              <VStack className="mt-6" spacing={12}>
                <H4 className="font-medium text-dark-100 lg:text-sm">Lose</H4>

                {betLose?.map((item, index) => (
                  <BetInfoItem key={item.id} {...item} type="lose" />
                ))}
              </VStack>
            </Show>
          </>
        </Show>

        <Show when={Boolean(!battle?.is_resolved)}>
          <VStack className="mt-4">
            {yourBets?.map((item, index) => (
              <BetInfoItem key={item.id} {...item} />
            ))}
          </VStack>
        </Show>
      </div>

      <VStack className="mt-5" spacing={16}>
        <Show when={betWins?.length > 0}>
          <HStack className="text-sm uppercase" pos="apart">
            <span className="font-medium text-dark-600">total payout</span>

            <span className="font-semibold text-[#008050]">
              {formatNumber(totalPayout)} ({formatNumber(betWins?.[0]?.potential_payout_ratio, 2)}%)
            </span>
          </HStack>

          <Show when={battle?.is_manual_admin_refund}>
            <p className="mb-1 text-center font-semibold text-[#00000099] text-xs lg:text-sm">
              You have successfully received your reward for this battle. Kindly check your wallet!{' '}
            </p>
          </Show>
        </Show>

        <div>
          {betWins?.length > 0 ? (
            <VStack spacing={16}>
              <Show when={!battle?.is_manual_admin_refund}>
                <ButtonClaim />
              </Show>

              <ButtonShare
                {...battle}
                bottomBar={
                  <HStack className="mt-1 w-full space-x-16 font-geistMono uppercase md:mt-3 lg:mt-5" pos="center" spacing={0} noWrap>
                    <VStack align="center" spacing={0}>
                      <span className="font-semibold text-[#FFFFFF] text-xxs">bet</span>
                      <span className="font-bold text-[#FFFFFF] text-[21px]">{formatNumber(agentWin?.current_bet)}</span>
                    </VStack>
                    <VStack align="center" spacing={0}>
                      <span className="font-semibold text-[#FFFFFF] text-xxs">potential payout</span>
                      <span className="font-bold text-[#FFFFFF] text-[21px]">{formatNumber(agentWin?.potentialPayout)}</span>
                    </VStack>
                    <VStack align="center" spacing={0}>
                      <span className="font-semibold text-[#FFFFFF] text-xxs">Roi</span>
                      <span className="font-bold text-[#31FF08] text-[21px]">{formatNumber(agentWin?.potentialPayoutRatio)} %</span>
                    </VStack>
                  </HStack>
                }
              >
                <Button className="w-full">Share Your Bets</Button>
              </ButtonShare>
            </VStack>
          ) : (
            <>
              {isBattleStillBet ? (
                <ButtonShare
                  {...battle}
                  topBar={<Tabs className="border" data={DATA_AGENT_TABS} tabs={tab} setTabs={setTab} tabClassName="px-7" />}
                  bottomBar={
                    <HStack className="mt-1 w-full space-x-16 font-geistMono uppercase md:mt-3 lg:mt-5" pos="center" spacing={0} noWrap>
                      <VStack align="center" spacing={0}>
                        <span className="font-semibold text-[#FFFFFF] text-xxs">to win</span>
                        <span className="font-bold text-[#FFFFFF] text-[21px]">{agentSelect?.label}</span>
                      </VStack>
                      <VStack align="center" spacing={0}>
                        <span className="font-semibold text-[#FFFFFF] text-xxs">bet</span>
                        <span className="font-bold text-[#FFFFFF] text-[21px]">{formatNumber(agentSelect?.current_bet)}</span>
                      </VStack>
                      <VStack align="center" spacing={0}>
                        <span className="font-semibold text-[#FFFFFF] text-xxs">potential payout</span>
                        <span className="font-bold text-[#FFFFFF] text-[21px]">{formatNumber(agentSelect?.potentialPayout)}</span>
                      </VStack>
                      <VStack align="center" spacing={0}>
                        <span className="font-semibold text-[#FFFFFF] text-xxs">Roi</span>
                        <span className="font-bold text-[#31FF08] text-[21px]">{formatNumber(agentSelect?.potentialPayoutRatio)} %</span>
                      </VStack>
                    </HStack>
                  }
                >
                  <Button className="w-full">Share Your Bets</Button>
                </ButtonShare>
              ) : (
                <Button className="w-full" onClick={() => onButtonClick?.()} disabled={!isBattleStillBet}>
                  PENDING DECISION
                </Button>
              )}
            </>
          )}
        </div>
      </VStack>
    </div>
  );
};

export default YourBets;
