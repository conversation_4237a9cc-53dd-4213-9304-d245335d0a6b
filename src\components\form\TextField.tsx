'use client';

import type { Control, FieldPath, FieldPathValue, FieldValues } from 'react-hook-form';

import { cn } from '@/libs/utils';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import type { InputProps } from '../ui/input';
import { Input } from '../ui/input';
import { Show } from '../utilities';

interface Props<T extends FieldValues = FieldValues> extends InputProps {
  control: Control<T>;
  name: FieldPath<T>;
  defaultValue?: FieldPathValue<T, FieldPath<T>>;
  label?: string;
  labelClassName?: string;
  required?: boolean;
}

const TextField = <T extends FieldValues>({
  className,
  labelClassName,
  control,
  defaultValue,
  label,
  required,
  variant,
  ...props
}: Props<T>) => {
  return (
    <FormField
      defaultValue={defaultValue}
      control={control}
      name={props.name}
      render={({ field, fieldState }) => (
        <FormItem>
          <FormControl>
            <div className="space-y-1">
              <Show when={!!label}>
                <FormLabel className={cn('uppercase', labelClassName)}>
                  {label} {required && <span className="text-red-500">*</span>}
                </FormLabel>
              </Show>
              <Input
                {...field}
                {...props}
                variant={variant}
                className={cn(className, {
                  'border-red-500 outline-red-500': !!fieldState.error,
                })}
              />
              <FormMessage className="mt-1 text-xs" />
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export { TextField };
