export const data_tabs = [
  {
    label: 'Overview',
    value: 'overview',
  },
  {
    label: 'Vote History',
    value: 'vote_history',
  },
];

export const data_battle = [
  {
    label: 'Overview',
    value: 'overview',
  },
  {
    label: 'Battle History',
    value: 'battle_history',
  },
];

export const color_status = (status: string) => {
  switch (status) {
    case 'WON':
      return 'bg-[#05C477]';
    case 'LOST':
      return 'bg-[#E0384B]';
    case 'Winner':
      return 'bg-[#05C477]';
    case 'Loser':
      return 'bg-[#E0384B]';
    default:
      return 'bg-[#D38C58]';
  }
};

export const date_filter = [
  {
    label: 'All time',
    value: 'All time',
  },
  {
    label: '7 days',
    value: '7 days',
  },
  {
    label: '30 days',
    value: '30 days',
  },
];
