import { cn } from '@/libs/common';
import { useEffect, useRef } from 'react';
import { HStack, VStack } from './utilities';

export const TimeBlock = ({ value, delay, className }: { value: string; delay?: number; className?: string }) => {
  return (
    <VStack spacing={12}>
      <HStack spacing={4}>
        <AnimatedDigitColumn digit={parseInt(value[0])} delay={Number(delay) * 4} className={className} />
        <AnimatedDigitColumn digit={parseInt(value[1])} delay={delay} className={className} />
      </HStack>
    </VStack>
  );
};

export const AnimatedDigitColumn = ({ digit, delay = 0, className }: { digit: number; delay?: number; className?: string }) => {
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const height = 16;
    if (wrapperRef.current) {
      wrapperRef.current.style.transitionDuration = `${digit * delay}ms`;
      wrapperRef.current.style.transform = `translateY(-${digit * height}px)`;
    }
  }, [digit]);

  const digits = Array.from({ length: 10 }, (_, i) => i);

  return (
    <div className="h-[16px] w-[8px] overflow-hidden rounded">
      <div ref={wrapperRef} className="transition-all duration-300 ease-in-out">
        {digits.map((d) => (
          <div
            style={{ height: '16px' }}
            key={d}
            className={cn('flex h-[16px] items-center justify-center font-geistMono font-medium text-dark-100 uppercase', className)}
          >
            {d}
          </div>
        ))}
      </div>
    </div>
  );
};
