import React from 'react';
import { TimeBlock } from './CountdowAnimation';
import { HStack, VStack } from './utilities';

interface BattleStatusDisplayProps {
  status?: string;
  remainingTime?: string;
  duration?: number;
  minutes: string;
  seconds: string;
}

const BattleStatusDisplay: React.FC<BattleStatusDisplayProps> = ({ status, remainingTime, duration, minutes, seconds }) => {
  if (status === 'ongoing') {
    return remainingTime === 'Choosing Winner...' ? (
      <span className="font-medium text-dark-100 text-sm uppercase">Choosing Winner...</span>
    ) : (
      <VStack spacing={8}>
        <span className="font-medium text-dark-600 text-sm uppercase">CLOSING IN</span>
        <HStack spacing={8}>
          <TimeBlock value={minutes} delay={100} className="text-sm" />
          <span className="font-medium text-base text-dark-100">:</span>
          <TimeBlock value={seconds} delay={100} className="text-sm" />
        </HStack>
      </VStack>
    );
  }

  if (status === 'upcoming') {
    return (
      <VStack spacing={8}>
        <span className="font-medium text-dark-600 text-sm uppercase">DURATION</span>
        <p className="font-medium text-dark-100 text-sm uppercase">{duration}m</p>
      </VStack>
    );
  }

  if (status === 'completed') {
    return <span className="font-medium text-dark-100 text-sm uppercase">Battle Ended</span>;
  }

  return null;
};

export default BattleStatusDisplay;
