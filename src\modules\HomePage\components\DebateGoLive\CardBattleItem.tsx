import type { IDataBattleDashboard } from '@/api/battles/types';
import { Button } from '@/components/ui/button';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import moment from 'moment';
import { useRouter } from 'next-nprogress-bar';
import Image from 'next/image';

const CardBattleItem = ({ item, loading }: { item?: IDataBattleDashboard; loading?: boolean }) => {
  const router = useRouter();

  return (
    <VStack spacing={20} className="relative z-1 h-full cursor-pointer justify-between gap-8 self-stretch rounded-xl bg-[#FFFFFF] p-8">
      <VStack className="w-full flex-1 items-center justify-between gap-6 px-12">
        <HStack
          pos={'apart'}
          noWrap
          className="-space-x-7 relative w-full items-center justify-center self-stretch transition-all duration-200"
        >
          <SkeletonWrapper loading={loading}>
            <div className="-rotate-[7deg] rounded-2xl ">
              <Image
                src={item?.agent1?.image_url || '/images/no-image.png'}
                width={136}
                height={136}
                alt="agent1"
                className={cn('h-[136px] w-[136px] origin-right rounded-2xl object-cover', {
                  'opacity-25 brightness-150 grayscale': item?.winner_id && item?.winner_id === item?.agent2.id && !!item?.winner?.id,
                })}
              />
            </div>
          </SkeletonWrapper>

          <SkeletonWrapper loading={loading}>
            <div className="rotate-[7deg] rounded-2xl ">
              <Image
                src={item?.agent2?.image_url || '/images/no-image.png'}
                width={136}
                height={136}
                alt="agent2"
                className={cn('h-[136px] w-[136px] origin-left rounded-2xl object-cover transition-all duration-200 ', {
                  'opacity-25 brightness-150 grayscale': item?.winner_id && item?.winner_id === item?.agent1.id && !!item?.winner?.id,
                })}
              />
            </div>
          </SkeletonWrapper>

          <div className="absolute top-[30%] left-1/2 h-[45px] w-[50px] translate-x-[5.5%] rotate-[5deg] ">
            <Image src="/images/vs.png" alt="logo" height={50} width={62} className="transition-all duration-200" />
          </div>
        </HStack>

        <VStack className="flex-1 gap-2">
          <SkeletonWrapper loading={loading}>
            <HStack spacing={12} className="flex-nowrap justify-center text-center font-jersey15 text-[2rem] uppercase">
              <span className="break-word line-clamp-1"> {item?.agent1?.name}</span>
              <span className="font-bold font-openSans text-[#131313] text-sm leading-[140%] tracking-[-0.14px]">
                {item?.agent1?.name && item?.agent2?.name ? 'x' : null}
              </span>
              <span className="break-word line-clamp-1">{item?.agent2?.name}</span>
            </HStack>
          </SkeletonWrapper>
          <p className="line-clamp-3 text-center font-geistMono font-medium text-[#717171] text-sm uppercase leading-[150%] tracking-[-0.14px]">
            {item?.topic}
          </p>
        </VStack>
      </VStack>

      <HStack align="center" className="" pos="center">
        <Button onClick={() => router.push(`/battles/${item?.id}/${item?.slug}`)}>VOTE WITH A BET</Button>
      </HStack>

      <HStack pos={'apart'} className="w-full py-3 font-geistMono uppercase">
        <VStack>
          <SkeletonWrapper loading={loading}>
            <p className="font-medium text-[#717171] text-xxs uppercase lg:text-xs">Start Time</p>
          </SkeletonWrapper>
          <SkeletonWrapper loading={loading}>
            <p className="font-semibold text-[#131313] text-xxs lg:text-xs">{moment(item?.start_at).format('MM/DD/YYYY - hh:mm A')}</p>
          </SkeletonWrapper>
        </VStack>

        <VStack>
          <SkeletonWrapper loading={loading}>
            <p className="font-medium text-[#717171] text-xxs uppercase lg:text-xs">Last Vote In</p>
          </SkeletonWrapper>
          <SkeletonWrapper loading={loading}>
            <p className="font-semibold text-[#131313] text-xxs lg:text-xs">
              {/* {moment(item?.start_at).format('MM/DD/YYYY - hh:mm A')} */}
              Coming soon...
            </p>
          </SkeletonWrapper>
        </VStack>
      </HStack>
    </VStack>
  );
};

export default CardBattleItem;
