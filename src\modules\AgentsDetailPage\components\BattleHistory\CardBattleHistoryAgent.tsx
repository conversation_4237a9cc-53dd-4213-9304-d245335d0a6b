import type { IDataBattleHistory } from '@/api/agent/types';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

const CardBattleHistoryAgent = ({ item, loading }: { item?: IDataBattleHistory; loading?: boolean }) => {
  const [isHover, setIsHover] = useState<boolean>(false);

  return (
    <Link href={`/battles/${item?.id}/${item?.slug}}`}>
      <VStack
        onMouseMove={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
        spacing={20}
        className="group z-1 h-full cursor-pointer justify-between gap-8 self-stretch rounded-xl bg-[#FFF] p-8 md:max-w-[424px]"
      >
        <VStack className="w-full flex-1 items-center justify-between gap-6">
          <HStack
            pos={'apart'}
            noWrap
            className="-space-x-7 relative w-full items-center justify-center self-stretch transition-all duration-200 group-hover:scale-[1.2]"
          >
            <SkeletonWrapper loading={loading}>
              <div
                className={cn('-rotate-[7deg] z-20 rounded-2xl', {
                  'z-10': item?.agent1?.result === 'LOST',
                })}
                style={{
                  boxShadow:
                    item?.agent1?.result === 'WON'
                      ? !isHover
                        ? `-154.88px 197.281px 70.063px 0px ${item?.agent1?.background}00, -99.56px 125.375px 64.531px 0px ${item?.agent1?.background}05, -23.97px 31.344px 40.563px 0px ${item?.agent1?.background}2E`
                        : `-4.216px 5.623px 16.87px 0px ${item?.agent1?.background}33, -18.276px 23.899px 30.928px 0px ${item?.agent1?.background}2E, -9.77px 53.421px 40.768px 0px ${item?.agent1?.background}1A, -75.912px 95.595px 49.203px 0px ${item?.agent1?.background}05, -118.092px 150.421px 53.421px 0px ${item?.agent1?.background}00`
                      : undefined,
                }}
              >
                <Image
                  src={item?.agent1?.image_url || '/images/no-image.png'}
                  width={136}
                  height={136}
                  alt="agent1"
                  className={cn('h-[136px] w-[136px] origin-right rounded-2xl object-cover', {
                    'opacity-25 brightness-150 grayscale': item?.agent1?.result === 'LOST',
                  })}
                />
              </div>
            </SkeletonWrapper>

            <SkeletonWrapper loading={loading}>
              <div
                className={cn('z-20 rotate-[7deg] rounded-2xl', {
                  'z-10': item?.agent2?.result === 'LOST',
                })}
                style={{
                  boxShadow:
                    item?.agent2?.result === 'WON'
                      ? !isHover
                        ? `84px 107px 38px 0px ${item?.agent2?.background}05, 54px 68px 35px 0px ${item?.agent2?.background}14, 30px 38px 29px 0px ${item?.agent2?.background}1A, 13px 17px 22px 0px ${item?.agent2?.background}00, 3px 4px 12px 0px ${item?.agent2?.background}00`
                        : `2.582px 3.443px 10.329px 0px ${item?.agent2?.background}33, 11.19px 14.633px 18.937px 0px ${item?.agent2?.background}2E, 25.823px 32.709px 24.962px 0px ${item?.agent2?.background}1A, 20.658px 58.532px 30.127px 0px ${item?.agent2?.background}14, 72.304px 92.101px 32.709px 0px ${item?.agent2?.background}05`
                      : undefined,
                }}
              >
                <Image
                  src={item?.agent2?.image_url || '/images/no-image.png'}
                  width={136}
                  height={136}
                  alt="agent2"
                  className={cn('h-[136px] w-[136px] origin-right rounded-2xl object-cover', {
                    'opacity-25 brightness-150 grayscale': item?.agent2?.result === 'LOST',
                  })}
                />
              </div>
            </SkeletonWrapper>

            <div className="absolute top-[30%] left-1/2 z-30 h-[45px] w-[50px] translate-x-[5.5%] rotate-[5deg] ">
              <Image
                src="/images/vs.png"
                alt="logo"
                height={50}
                width={62}
                className="transition-all duration-200 group-hover:translate-y-[8px] group-hover:scale-[1.2]"
              />
            </div>
          </HStack>

          <VStack className="flex-1 gap-2">
            <SkeletonWrapper loading={loading}>
              <HStack className="flex-nowrap justify-center gap-3 text-center font-jersey15 text-[2rem] uppercase">
                <span className="break-word line-clamp-1"> {item?.agent1?.name}</span>
                <span className="font-bold font-openSans text-[#131313] text-sm leading-[140%] tracking-[-0.14px]">x</span>
                <span className="break-word line-clamp-1">{item?.agent2?.name}</span>
              </HStack>
            </SkeletonWrapper>
            <p className="line-clamp-3 text-center font-geistMono font-medium text-[#717171] text-sm uppercase leading-[150%] tracking-[-0.14px]">
              {item?.topic}
            </p>
          </VStack>

          <SkeletonWrapper loading={loading}>
            <Show when={item?.status && item.status === 'LOST'}>
              <HStack
                noWrap
                className="rounded-[9px] bg-[#131313] px-3 py-2 text-center font-geistMono font-semibold text-[#FF3901] text-xs uppercase not-italic leading-[150%] tracking-[-0.12px]"
              >
                K.O.
              </HStack>
            </Show>
            <Show when={item?.status && item.status === 'WON'}>
              <HStack
                noWrap
                className="rounded-[9px] bg-[#131313] px-3 py-2 text-center font-geistMono font-semibold text-[#31FF08] text-xs uppercase"
              >
                {item?.agent1?.result === 'WON' ? item?.agent1?.name : item?.agent2?.name} WINS!
              </HStack>
            </Show>
          </SkeletonWrapper>
        </VStack>

        <VStack className="items-center">
          <SkeletonWrapper loading={loading}>
            <p className="text-center font-medium text-[#717171] text-xs uppercase leading-[150%]">Battle ended</p>
          </SkeletonWrapper>
          <SkeletonWrapper loading={loading}>
            <p className="text-center font-medium text-[#131313] text-xs uppercase leading-[150%]">
              {moment(item?.start_at).add(Number(item?.duration), 'minutes').format('MM/DD/YYYY - hh:mm A')}
            </p>
          </SkeletonWrapper>
        </VStack>
      </VStack>
    </Link>
  );
};

export default CardBattleHistoryAgent;
