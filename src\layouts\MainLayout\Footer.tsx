import { Icons } from '@/assets/icons';
import { HStack } from '@/components/utilities';
import { ROUTER } from '@/libs/router';
import { format } from 'date-fns';
import Link from 'next/link';

const Footer = () => {
  return (
    <HStack className="border-[#0000001A] border-t bg-white font-geistMono backdrop-blur-[48px]">
      <div className="mx-auto flex w-full max-w-[1312px] flex-col items-center justify-between gap-5 px-2 py-4 md:flex-row">
        <HStack spacing={12}>
          <Icons.copyright />

          <p className="font-medium text-[#00000099] text-xs uppercase sm:text-sm">
            {format(new Date(), 'yyyy')} Battle of Agents, All rights reserved.
          </p>
        </HStack>
        <HStack spacing={24} className="font-medium text-[#00000099] text-xs uppercase sm:text-sm">
          <Link href="" className="hover:font-semibold">
            FAQs
          </Link>
          <Link href={ROUTER.TERMS_OF_SERVICE} className="hover:font-semibold">
            Terms of Service
          </Link>
          <Link href={ROUTER.PRIVACY_POLICY} className="hover:font-semibold">
            Privacy Policy
          </Link>
        </HStack>
      </div>
    </HStack>
  );
};

export default Footer;
