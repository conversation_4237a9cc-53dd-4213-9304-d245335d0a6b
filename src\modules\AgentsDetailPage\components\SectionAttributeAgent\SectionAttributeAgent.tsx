import type { IOptionAttribute } from '@/api/agent/types';
import { Icons } from '@/assets/icons';
import type { ReactNode } from 'react';
import ItemPersonality from '../ItemListSection';
import WrapperSectionAgentDetail from '../WrapperSectionAgentDetail';

type Props = {
  icon: ReactNode;
  title: string;
  data: IOptionAttribute[];
  index: number;
};

const ListIconsAttribute = [Icons.sword, Icons.evaluation, Icons.strength, Icons.weakness];

const SectionAttributeAgent = (props: Props) => {
  const { title, data, index } = props;
  const Icon = ListIconsAttribute?.[index] || ListIconsAttribute?.[0];

  return (
    <WrapperSectionAgentDetail title={title} icon={<Icon />}>
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        {data.map((item: IOptionAttribute, index: number) => (
          <ItemPersonality key={index} title={item?.metric} description={item?.properties} />
        ))}
      </div>
    </WrapperSectionAgentDetail>
  );
};

export default SectionAttributeAgent;
