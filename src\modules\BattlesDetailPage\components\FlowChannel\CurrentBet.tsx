'use client';

import { useBattleReportById } from '@/api/battles/queries';
import { HStack, Show, VStack } from '@/components/utilities';
import { useUserLogin } from '@/hooks/useUserLogin';
import { onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import { ChevronDown, ChevronLeft } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const CurrentBet = () => {
  const [visible, setVisible] = useState(true);
  const toggleVisibility = () => {
    setVisible(!visible);
  };
  const { id } = useParams();
  const { isLoggedIn } = useUserLogin();

  const { data, isFetching } = useBattleReportById({ variables: String(id), enabled: Boolean(id && isLoggedIn), onError: onMutateError });

  const isShow = Boolean(
    id && isLoggedIn && (data?.agent1_data || data?.agent2_data) && (data?.agent1_data?.fee || data?.agent2_data?.fee)
  );

  if (!isShow) return <></>;
  return (
    <VStack className="rounded-xl bg-[#FFFFFF26] p-4" spacing={20}>
      <HStack noWrap pos="apart">
        <p className="font-geist-mono font-semibold text-tertiary-900 text-xl">Your current bets</p>
        {visible ? (
          <ChevronDown size={20} color="#D38C58" className="cursor-pointer" onClick={toggleVisibility} />
        ) : (
          <ChevronLeft color="#D38C58" size={20} className="rotate-180 cursor-pointer" onClick={toggleVisibility} />
        )}
      </HStack>
      <Show when={visible}>
        <VStack spacing={8} className="cursor-default" onClick={(e) => e.stopPropagation()}>
          {data?.agent1_data && data?.agent1_data?.current_bet > 0 && (
            <div className="flex flex-col gap-1 rounded-md bg-[#FFFFFF59] px-4 py-3">
              <HStack noWrap pos="apart">
                <p className="font-semibold text-primary-700">{data?.agent1_data?.name}</p>
                <p className="text-primary-700">Potential Payout</p>
              </HStack>
              <HStack noWrap pos="apart">
                <p className="flex items-center font-semibold text-primary-700">{formatNumber(Number(data?.agent1_data?.current_bet))}</p>
                <p className="font-semibold text-shamrock-700">
                  {formatNumber(Number(data?.agent1_data?.potential_payout))} (
                  {formatNumber(Number(data?.agent1_data?.potential_payout_ratio), 2)}%)
                </p>
              </HStack>
            </div>
          )}

          {data?.agent2_data && data?.agent2_data?.current_bet > 0 && (
            <div className="flex flex-col gap-1 rounded-md bg-[#FFFFFF59] px-4 py-3">
              <HStack noWrap pos="apart">
                <p className="font-semibold text-primary-700">{data?.agent2_data?.name}</p>
                <p className="text-primary-700">Potential Payout</p>
              </HStack>
              <HStack noWrap pos="apart">
                <p className="flex items-center font-semibold text-primary-700">{formatNumber(Number(data?.agent2_data?.current_bet))}</p>
                <p className="font-semibold text-shamrock-700">
                  {formatNumber(Number(data?.agent2_data?.potential_payout))} (
                  {formatNumber(Number(data?.agent2_data?.potential_payout_ratio), 2)}%)
                </p>
              </HStack>
            </div>
          )}
        </VStack>
      </Show>
    </VStack>
  );
};

export default CurrentBet;
