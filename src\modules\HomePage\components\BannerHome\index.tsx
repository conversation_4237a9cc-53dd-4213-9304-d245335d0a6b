import { useListBannerHome } from '@/api/home-page/queries';
import { useRef } from 'react';

const BannerHome = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const { data, isFetching } = useListBannerHome();

  return <></>;
  // return (
  //   <>
  //     <Banner />
  //     <div className="relative flex w-full items-center justify-center">
  //       <button
  //         ref={prevRef}
  //         className="absolute left-[-25px] z-10 rounded-sm border border-white/15 bg-white/15 p-2 text-white backdrop-blur-md"
  //       >
  //         <ChevronLeft size={24} />
  //       </button>
  //       <button
  //         ref={nextRef}
  //         className="absolute right-[-25px] z-10 rounded-sm border border-white/15 bg-white/15 p-2 text-white backdrop-blur-md"
  //       >
  //         {/* <ChevronRight size={24} /> */}
  //       </button>
  //       <Show when={!isFetching}>
  //         <Swiper
  //           modules={[Navigation]}
  //           spaceBetween={20}
  //           slidesPerView={1.8}
  //           navigation={{
  //             prevEl: prevRef.current,
  //             nextEl: nextRef.current,
  //           }}
  //           loop
  //           onInit={(swiper) => {
  //             setTimeout(() => {
  //               if (swiper.params.navigation) {
  //                 (swiper.params.navigation as NavigationOptions).prevEl = prevRef.current;
  //                 (swiper.params.navigation as NavigationOptions).nextEl = nextRef.current;
  //                 swiper.navigation.init();
  //                 swiper.navigation.update();
  //               }
  //             });
  //           }}
  //         >
  //           {data?.slice(0, 5).map((item, index) => {
  //             return (
  //               <SwiperSlide key={index}>
  //                 <BannerCard item={item} />
  //               </SwiperSlide>
  //             );
  //           })}
  //         </Swiper>
  //       </Show>
  //       <Show when={isFetching}>
  //         <Swiper
  //           modules={[Navigation]}
  //           spaceBetween={20}
  //           slidesPerView={1.8}
  //           navigation={{
  //             prevEl: prevRef.current,
  //             nextEl: nextRef.current,
  //           }}
  //           loop
  //           onInit={(swiper) => {
  //             setTimeout(() => {
  //               if (swiper.params.navigation) {
  //                 (swiper.params.navigation as NavigationOptions).prevEl = prevRef.current;
  //                 (swiper.params.navigation as NavigationOptions).nextEl = nextRef.current;
  //                 swiper.navigation.init();
  //                 swiper.navigation.update();
  //               }
  //             });
  //           }}
  //         >
  //           <SwiperSlide>
  //             <BannerCard loading={isFetching} />
  //           </SwiperSlide>
  //           <SwiperSlide>
  //             <BannerCard loading={isFetching} />
  //           </SwiperSlide>
  //         </Swiper>
  //       </Show>
  //     </div>
  //   </>
  // );
};

export default BannerHome;
