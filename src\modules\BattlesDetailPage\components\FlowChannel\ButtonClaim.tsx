'use client';
import { useCurrentUserClaim } from '@/api/battles/queries';
import { Button } from '@/components/ui/button';
import { onMutateError } from '@/libs/common';
import { SMART_CONTRACT_ADDRESS } from '@/libs/const';
import { ABI } from '@/libs/web3/abi';
import { customChain } from '@/libs/web3/wagmi';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useWaitForTransactionReceipt, useWriteContract } from 'wagmi';

const ButtonClaim = () => {
  const { id } = useParams();
  const [claimSuccess, setClaimSuccess] = useState<boolean>(false);

  const { data: dataHash, writeContract, isLoading } = useWriteContract();
  const { isFetching: isFetchingTransaction, status } = useWaitForTransactionReceipt({
    chainId: customChain.id,
    hash: dataHash,
    query: {
      enabled: !!dataHash,
    },
  });
  const {
    data: dataClaim,
    isFetching: isFetchingClaim,
    refetch,
  } = useCurrentUserClaim({
    variables: String(id),
    enabled: !!id,
    onError: onMutateError,
  });

  const handleClaim = () => {
    writeContract({
      abi: ABI,
      address: SMART_CONTRACT_ADDRESS,
      functionName: 'userClaim',
      args: [BigInt(String(id))],
    });
  };

  useEffect(() => {
    if (status === 'success' && !isFetchingTransaction) {
      toast.success('Your prize has been claimed successfully!');
      setClaimSuccess(true);
      refetch();
    }
    if (status === 'error' && !isFetchingTransaction) {
      toast.error('Failed to claim the prize. Please try again!');
    }
  }, [status, isFetchingTransaction]);

  return (
    <div>
      {dataClaim || claimSuccess ? (
        <Button disabled className="w-full">
          Claimed
        </Button>
      ) : (
        <Button className="w-full bg-[#31FF08]" onClick={handleClaim} loading={isFetchingTransaction || isLoading || isFetchingClaim}>
          Claim Prize
        </Button>
      )}
    </div>
  );
};

export default ButtonClaim;
