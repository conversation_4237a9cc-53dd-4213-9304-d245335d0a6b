'use client';
import type { IYourBet } from '@/api/battles/types';
import H4 from '@/components/text/H4';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import React from 'react';

type Props = {
  loading?: boolean;
  type?: 'win' | 'lose';
} & IYourBet;

const BetInfoItem = ({ name, image_url, bet_amount, potential_payout, potential_payout_ratio, type }: Props) => {
  return (
    <div className="flex rounded-lg bg-white px-2 py-3">
      <div className="">
        <Image src={image_url} alt="" width={48} height={48} className="h-12 w-12 rounded-sm" />
      </div>

      <div className="ml-3 flex-1">
        <H4 className="font-medium text-dark-100 uppercase lg:text-sm">{name}</H4>

        <span className="font-normal text-dark-600 text-sm">{bet_amount}</span>
      </div>

      <div className="font-normal text-sm uppercase">
        <div className="text-right text-dark-600">PAYOUT</div>
        {type === 'lose' ? (
          <div className="text-right font-semibold text-dark-100">0</div>
        ) : (
          <div className="text-right font-semibold text-shamrock-700">
            {formatNumber(potential_payout, 5)} ({formatNumber(potential_payout_ratio, 2)}%)
          </div>
        )}
      </div>
    </div>
  );
};

export default BetInfoItem;
