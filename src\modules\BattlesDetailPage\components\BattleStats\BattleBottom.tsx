import { useJudgeAgent } from '@/api/agent/queries';
import type { IBattleDetail } from '@/api/battles/types';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import Link from 'next/link';

type Props = {
  data?: IBattleDetail;
  stats?: {
    count_bet: number;
    prize_pool: number;
  };
};
const BattleBottom = ({ data, stats }: Props) => {
  const { data: dataAgent, isFetching } = useJudgeAgent({ onError: onMutateError });

  return (
    <div className="absolute right-0 bottom-6 left-0 grid grid-cols-2 justify-center gap-4 md:grid-cols-[2.6fr_1fr_2.6fr]">
      <Show when={!data?.is_resolved}>
        <VStack spacing={8} className="col-span-2 md:col-auto" align="center">
          <span className="text-center font-semibold text-[#D0D0D0] text-sm uppercase md:text-left md:text-[#FFFFFF]">JUDGES</span>
          <SkeletonWrapper loading={isFetching}>
            <HStack spacing={8} className="justify-center">
              {dataAgent?.map((i, z) => (
                <Tooltip className="py-1 text-xs" label={i?.name} key={z}>
                  <div className="h-8 w-8 cursor-pointer rounded-sm hover:opacity-50 md:h-6 md:w-6">
                    <Link href={`/agents/${i?.id}?type=JUDGE_AGENT`}>
                      <Image src={i?.image_url} alt={i?.name} className="h-full w-full rounded-sm object-cover" width={100} height={100} />
                    </Link>
                  </div>
                </Tooltip>
              ))}
            </HStack>
          </SkeletonWrapper>
        </VStack>
      </Show>

      <VStack className="text-[#FFFFFF] uppercase" spacing={4} align="center">
        <span className="font-semibold text-[#D0D0D0] text-sm uppercase md:text-[#FFFFFF]">PRIZE POOL</span>
        <span className="font-bold text-4xl">{formatNumber(Number(stats?.prize_pool))}</span>
      </VStack>

      <VStack className="text-[#FFFFFF] uppercase" spacing={4} align="center">
        <span className="font-semibold text-[#D0D0D0] text-sm uppercase md:text-[#FFFFFF]">TOTAL BETS</span>
        <span className="font-bold text-4xl">{formatNumber(stats?.count_bet)}</span>
      </VStack>
    </div>
  );
};

export default BattleBottom;
