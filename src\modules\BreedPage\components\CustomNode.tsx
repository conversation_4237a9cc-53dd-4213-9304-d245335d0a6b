import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show, VStack } from '@/components/utilities';
import { Lock } from 'lucide-react';
import Link from 'next/link';
import { Handle, type NodeProps, Position } from 'reactflow';
import useTimeDifference from './UseTimeDifference';

const CustomNode: React.FC<NodeProps> = ({ data }) => {
  const { timeDifference } = useTimeDifference(String(data?.created_at));
  return (
    <>
      <Show when={!data?.cardEmpty}>
        <VStack className="relative max-w-[424px] rounded-xl border border-white/35 bg-white/35 p-4 backdrop-blur-[250px]" spacing={20}>
          <div className="absolute top-0 right-0 w-fit rounded-tr-[11px] rounded-bl-[4px] border border-[#5832201A] bg-red-damask-500 px-3 py-2 font-semibold text-sm text-white">
            GEN {data?.gen}
          </div>
          <HStack spacing={16}>
            <div className="relative h-[139px] w-[139px]">
              <img src={data?.image_url || '/images/no-image.png'} alt="" className="h-full w-full rounded-md" />
              <div
                className="absolute bottom-1 left-1 z-10 flex h-6 w-8 items-center justify-center rounded-[7.27px] border border-white/15 p-[3.64px] font-geistMono text-white text-xs"
                style={{ background: 'linear-gradient(97.6deg, rgba(237, 255, 245, 0.15) 3.17%, rgba(63, 81, 71, 0.15) 95.57%)' }}
              >
                #{data?.gen}
              </div>
            </div>
            <VStack className="flex-1 justify-between">
              <p className="font-geistMono font-semibold text-base text-tertiary-900">{data?.name}</p>
              <VStack className="gap-4 rounded-sm border border-white/25 bg-white/25 px-3 py-2 backdrop-blur">
                <HStack pos="apart">
                  <p className="font-medium text-sm text-tertiary-900">Age</p>
                  <p className="font-geistMono font-semibold text-sm text-tertiary-900">{timeDifference}</p>
                </HStack>
                <HStack pos="apart">
                  <p className="font-medium text-sm text-tertiary-900">Win Rate ({data?.win_count})</p>
                  <p className="font-geistMono font-semibold text-sm text-tertiary-900">{data?.rate?.toFixed(2)}%</p>
                </HStack>
                <HStack pos="apart">
                  <p className="font-medium text-sm text-tertiary-900">Total Debates</p>
                  <p className="font-geistMono font-semibold text-sm text-tertiary-900">{data?.debate_count}</p>
                </HStack>
              </VStack>
            </VStack>
          </HStack>
          <Tooltip label={data?.description} className="z-50 max-w-[400px] whitespace-pre-line">
            <p className="line-clamp-4 font-normal text-sm text-tertiary-900">{data?.description}</p>
          </Tooltip>
          <Show when={data?.capabilities?.length > 0}>
            <VStack spacing={8}>
              <p className="font-medium text-tertiary-900 text-xs">CAPABILITY</p>
              <HStack spacing={4}>
                {data?.capabilities?.map((cap: string, index: number) => (
                  <div key={index} className="rounded-2xl border border-gray-800 px-3 py-2 text-medium text-sm text-tertiary-900">
                    {cap}
                  </div>
                ))}
              </HStack>
            </VStack>
          </Show>
          <HStack spacing={8} noWrap>
            <Link href={`/agents/${data?.id}?type=${data?.agent_type}`} className="w-full whitespace-nowrap">
              <Button className="flex w-full items-center gap-2">
                View Agent Details <Icons.breedAttachment />
              </Button>
            </Link>
            <Link href={data?.social_url || ''} className="w-full whitespace-nowrap">
              <Button className="flex w-full items-center gap-2">
                View X account <Icons.breedX />
              </Button>
            </Link>
          </HStack>
          <Handle type="source" position={Position.Bottom} className="!bg-white" />
          <Handle type="target" position={Position.Top} className="opacity-0" />
        </VStack>
      </Show>
      <Show when={data?.cardEmpty}>
        <VStack className="relative w-[424px] rounded-xl border border-white/35 bg-white/35 p-4 backdrop-blur-[250px]" spacing={20}>
          <div className="absolute top-0 right-0 w-fit rounded-tr-[11px] rounded-bl-[4px] border border-[#5832201A] bg-red-damask-500 px-3 py-2 font-semibold text-sm text-white">
            GEN {data?.gen}
          </div>
          <HStack spacing={12}>
            <div className="rounded-sm border border-white/15 bg-white/15 px-2 py-[2px] font-geistMono text-white backdrop-blur">?</div>
            <p className="font-geistMono font-semibold text-base text-tertiary-900">GEN {data?.gen}</p>
          </HStack>
          <div
            className="flex h-[131px] w-full flex-col items-center justify-center gap-2 rounded-sm border border-white/25 text-center"
            style={{
              background:
                'linear-gradient(96.37deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 48.17%, rgba(255, 255, 255, 0.25) 100%)',
            }}
          >
            <Icons.cube />
            <p className="font-medium text-sm text-tertiary-900">1st Generation Agent</p>
          </div>
          <VStack spacing={12}>
            <HStack className="gap-2 rounded-sm border border-white/25 bg-white/25 px-3 py-2 backdrop-blur">
              <Lock color="#583220" size={16} />
              <p className="font-medium text-tertiary-900 text-xs">Information</p>
            </HStack>
            <HStack className="gap-2 rounded-sm border border-white/25 bg-white/25 px-3 py-2 backdrop-blur">
              <Lock color="#583220" size={16} />
              <p className="font-medium text-tertiary-900 text-xs">Description</p>
            </HStack>
            <HStack className="gap-2 rounded-sm border border-white/25 bg-white/25 px-3 py-2 backdrop-blur">
              <Lock color="#583220" size={16} />
              <p className="font-medium text-tertiary-900 text-xs">Capability</p>
            </HStack>
            <Button className="w-fit bg-red-damask-100 text-tertiary-900">PROTOTYPE</Button>
          </VStack>
        </VStack>
        <Handle type="source" position={Position.Bottom} className="!bg-white" />
        <Handle type="target" position={Position.Top} className="opacity-0" />
      </Show>
    </>
  );
};

export default CustomNode;
