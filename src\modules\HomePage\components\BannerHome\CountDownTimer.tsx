import { useListBattle } from '@/api/battles/queries';
import { AnimatedDigitColumn } from '@/components/CountdowAnimation';
import { HStack } from '@/components/utilities';
import useCountdownTimer from '@/hooks/useCountdownTimer';
import React, { useLayoutEffect } from 'react';

const CountDownTimer = () => {
  const { data, isFetching } = useListBattle({
    variables: { page: 1, limit: 1, sort_key: '', sort_type: 'DESC', status: 'upcoming' },
  });
  const targetDate = data?.data[0]?.start_at;
  const { start, seconds, days, hours, minutes } = useCountdownTimer();

  useLayoutEffect(() => {
    if (targetDate) {
      start(new Date(targetDate));
    }
  }, [targetDate]);

  if (isFetching || !targetDate)
    return (
      <div className="flex items-center justify-center">
        <p className="text-center font-medium text-[#717171] text-xs uppercase lg:text-sm">No battle coming soon</p>
      </div>
    );

  return (
    <div className="flex items-center justify-center space-x-1.5 lg:space-x-3">
      {days > 0 ? (
        <>
          <TimeBlock value={formatNumber(days)} label="DAYS" delay={0} />
          <span>:</span>
          <TimeBlock value={formatNumber(hours)} label="HRS" delay={800} />
          <span>:</span>
          <TimeBlock value={formatNumber(minutes)} label="MINS" delay={600} />
          <span>:</span>
          <TimeBlock value={formatNumber(seconds)} label="SECS" delay={100} />
        </>
      ) : (
        <>
          <TimeBlock value={formatNumber(hours)} label="HRS" delay={800} />
          <span>:</span>
          <TimeBlock value={formatNumber(minutes)} label="MINS" delay={600} />
          <span>:</span>
          <TimeBlock value={formatNumber(seconds)} label="SECS" delay={100} />
        </>
      )}
    </div>
  );
};

export default CountDownTimer;

function formatNumber(n: number) {
  return n < 10 ? `0${n}` : `${n}`;
}

export const TimeBlock = ({ value, label, delay }: { value: string; label: string; delay?: number }) => {
  return (
    <HStack spacing={12}>
      <HStack className="gap-0 md:gap-1">
        <AnimatedDigitColumn digit={parseInt(value[0])} delay={Number(delay) * 4} className="text-xs md:text-sm lg:text-base" />
        <AnimatedDigitColumn digit={parseInt(value[1])} delay={delay} className="text-xs md:text-sm lg:text-base" />
      </HStack>
      <p className="font-geistMono font-medium text-dark-600 text-xs uppercase md:text-sm lg:text-base">{label}</p>
    </HStack>
  );
};
