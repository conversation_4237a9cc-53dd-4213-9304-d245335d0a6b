'use client';

import { createAccount } from '@/api/auth/requests';
import { Icons } from '@/assets/icons';
import { CheckboxField, TextField } from '@/components/form';
import { UploadAvatarField } from '@/components/form/UploadAvatarField';
import H2 from '@/components/text/H2';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { FormWrapper } from '@/components/ui/form';
import { HStack, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import { ROUTER } from '@/libs/router';
import { useUserStore } from '@/stores/UserStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { setCookie } from 'cookies-next';
import Link from 'next/link';
import React from 'react';
import { type SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { getRandomAvatar } from './libs/utils';
import { type CreateAccountSchema, createAccountSchema } from './libs/validators';

type Props = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const CreateAccountDialog = ({ open = false, setOpen }: Props) => {
  const form = useForm<CreateAccountSchema>({
    defaultValues: {
      avatar: getRandomAvatar(),
      username: '',
    },
    resolver: zodResolver(createAccountSchema),
  });

  const { verifyToken, setIsVerifiedUser, setVerifyToken, setUser } = useUserStore();
  const { mutate, isLoading } = useMutation(createAccount);

  const handleSubmit: SubmitHandler<CreateAccountSchema> = (formData) => {
    mutate(
      { formData: { username: formData.username, avatar: formData.avatar } as any, token: verifyToken },
      {
        onSuccess: ({ access_token, access_token_ttl, verify_token, is_verified_user, refresh_token, refresh_token_ttl, user }) => {
          setIsVerifiedUser(is_verified_user);
          setVerifyToken('');
          setCookie('access_token', access_token, { maxAge: access_token_ttl * 60 });
          setCookie('refresh_token', refresh_token, {
            maxAge: refresh_token_ttl * 60,
          });
          setUser(user);
          setOpen(false);
          toast.success('Connect wallet successfully');
        },
        onError: onMutateError,
      }
    );
  };

  return (
    <>
      <Dialog open={open}>
        <DialogContent className="flex w-full max-w-[340px] flex-col rounded-[24px] px-6 sm:px-8 md:max-w-[473px]">
          <HStack pos="right">
            <button className="hover:opacity-75" onClick={() => setOpen(false)}>
              <Icons.X />
            </button>
          </HStack>

          <HStack pos="center" className="mt-3">
            <div className="rounded-lg bg-[#31FF08] px-3 py-2 font-semibold text-dark-100 text-sm uppercase">WALLET CONNECTED</div>
          </HStack>
          <H2 className="-mt-2 text-center font-bangers font-medium text-4xl text-[#000000] sm:mt-3 sm:mb-6 lg:text-5xl">
            Create your account
          </H2>

          <FormWrapper form={form} onSubmit={handleSubmit}>
            <VStack spacing={16} className="pb-10 sm:px-10">
              <div className="mb-6 flex justify-center sm:mb-5">
                <UploadAvatarField
                  control={form.control}
                  name="avatar"
                  accept={['image/png', 'image/webp', 'image/jpeg']}
                  className="rounded-full"
                />
              </div>

              <TextField
                required
                labelClassName="text-[#717171] text-xs lg:text-xs"
                label="USERNAME"
                name="username"
                placeholder="ENTER USERNAME"
                control={form.control}
                className="h-[46px] rounded-[8px] border-none bg-[#F3F3F3] text-xs placeholder:font-geistMono focus:bg-[#FFEB0033] sm:border-input sm:border-solid sm:bg-transparent"
              />

              <CheckboxField
                control={form.control}
                className="items-start"
                inputClassName="w-6 h-6 rounded-md"
                label={
                  <div className="-mt-1 ml-1 font-geistMono font-medium text-[#717171] text-sm uppercase lg:text-base">
                    I agree to BATLLE OF AGENTS’{' '}
                    <Link
                      onClick={() => {
                        setOpen(false);
                        setIsVerifiedUser(undefined);
                      }}
                      href={ROUTER.TERMS_OF_SERVICE}
                      className="text-[#131313] underline"
                    >
                      Terms of USE
                    </Link>{' '}
                    and{' '}
                    <Link
                      onClick={() => {
                        setOpen(false);
                        setIsVerifiedUser(undefined);
                      }}
                      href={ROUTER.PRIVACY_POLICY}
                      className="text-[#131313] underline"
                    >
                      Privacy Policy
                    </Link>
                    .
                  </div>
                }
                name="isConfirm"
              />

              <Button
                disabled={!form.formState.isValid}
                type="submit"
                className="w-full rounded-[8px] border border-dark-100 shadow-none"
                size="md"
                loading={isLoading}
              >
                Save
              </Button>
            </VStack>
          </FormWrapper>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreateAccountDialog;
