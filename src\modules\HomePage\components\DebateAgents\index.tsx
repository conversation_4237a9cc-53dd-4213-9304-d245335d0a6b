import { useParentAgent } from '@/api/agent/queries';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { HStack, Show } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import Link from 'next/link';
import WrapperSectionHome from '../WrapperSectionHome';
import CardDebaterAgents from './CardDebaterAgents';

const DebateAgents = () => {
  const { data, isFetching } = useParentAgent({ onError: onMutateError });
  return (
    <div className="bg-[#131313]">
      <WrapperSectionHome className="!gap-12">
        <HStack pos="apart" noWrap>
          <H1 className="text-[#FFF]">Top Agents</H1>

          <Link href={'/agents'}>
            <Button className="font-geistMono leading-[150%]">View All</Button>
          </Link>
        </HStack>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 sm:gap-5 lg:grid-cols-5">
          {data
            ?.sort((a, b) => b.debate_count - a.debate_count || b.debate_count - a.debate_count)
            ?.slice(0, 5)
            ?.map((item) => (
              <CardDebaterAgents key={item.id} {...item} />
            ))}
          <Show when={isFetching}>
            <CardDebaterAgents loading={isFetching} />
            <CardDebaterAgents loading={isFetching} />
          </Show>
        </div>
      </WrapperSectionHome>
    </div>
  );
};

export default DebateAgents;
