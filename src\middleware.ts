import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const decodeBase64 = (encodedText: string) => {
  return atob(encodedText);
};

export function middleware(request: NextRequest) {
  const value = request?.cookies?.get('password')?.value || '';
  const ua = request.headers.get('user-agent') || '';

  const isBot = /bot|crawl|spider|twitterbot|facebook|slack|discord/i.test(ua);

  if (!process.env.PASSWORD) {
    return Response.json({ success: false, message: 'Lack env PASSWORD' }, { status: 401 });
  }

  const listPassword = process.env.PASSWORD.split(',') || [];
  const isRedirect = listPassword.includes(decodeBase64(value));

  // if (isRedirect || request.url.includes('/pass') || isBot) {
  //   return NextResponse.next();
  // }

  // return NextResponse.redirect(new URL('/pass', request.url));

  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/agents/:path*', '/battles/:path*', '/breed/:path*'],
};
