include:
  - project: var-ai/devops-template
    file: fe-nextjs/gitlab-ci.yml
    inputs:
      project-name: battle ai fe

build:
  extends: .build-template
  tags:
    - vm-0g-runner

deploy-staging:
  extends: .deploy-template
  needs: [build]
  tags:
    - var-ai-runner
  rules:
    - if: '$CI_COMMIT_REF_NAME == "staging"'
      when: on_success

deploy-production:
  extends: .deploy-template
  needs: [build]
  tags:
    - vm-0g-runner
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: on_success
