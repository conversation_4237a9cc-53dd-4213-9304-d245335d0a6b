import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { HStack, Show, VStack } from '@/components/utilities';
import ConnectWallet from '@/components/wallet/ConnectWallet';
import { useUserLogin } from '@/hooks/useUserLogin';
import { cn } from '@/libs/common';
import { useDisclosure } from '@mantine/hooks';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { MENU_NAVS } from './libs/consts';
import MobileUserInfo from './mobile/MobileUserInfo';

const Sidebar = () => {
  const [opened, { toggle }] = useDisclosure(false);

  const { isLoggedIn } = useUserLogin();

  return (
    <>
      <HStack spacing={24}>
        <Button onClick={toggle} className="h-8">
          <Menu width={18} height={18} />
        </Button>
      </HStack>
      <Sheet open={opened} onOpenChange={toggle}>
        <SheetContent className="bg-white">
          <div className={'-mt-6 flex h-header items-center justify-between p-4'}>
            <Link className="-ml-4" href={'/'} scroll={false}>
              <Image src="/images/logo-header.png" alt="logo" width={80} height={32} />
            </Link>
          </div>

          <VStack align="center" className="mt-6 gap-2">
            {MENU_NAVS.map((nav) => {
              return (
                <Link
                  scroll={false}
                  href={nav.link}
                  key={nav.title}
                  className={cn(
                    'rounded-[7px] px-6 py-1.5 font-semibold text-[#131313] text-base uppercase hover:bg-[#FFEB00] hover:text-[#FF3901] hover:shadow-[_0_0_0_2px_#FF3901]'
                    // pathname === nav.link && 'text-primary-700'
                  )}
                >
                  {nav.title}
                </Link>
              );
            })}
            <Show when={!isLoggedIn}>
              <ConnectWallet>
                <Button className="mt-10 h-[46px] w-full px-3 py-1.5 font-geistMono font-semibold text-base text-black uppercase">
                  Connect wallet
                </Button>
              </ConnectWallet>
            </Show>
            <Show when={isLoggedIn}>
              <MobileUserInfo />
            </Show>
          </VStack>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default Sidebar;
