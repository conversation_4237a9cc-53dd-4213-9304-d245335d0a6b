import { VStack } from '@/components/utilities';
import { cn } from '@/libs/common';

export const MessageBubble = ({ agent, message, isCurrentUser, isLoading, showCursor }: any) => {
  return (
    <div className={`flex gap-2 font-geistMono text-sm ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`flex items-end gap-4 ${!isCurrentUser ? 'flex-row-reverse' : 'flex-row'}`}>
        <VStack className={cn(isCurrentUser ? 'items-end' : 'items-start')}>
          <span className="font-semibold text-[#00000099] text-sm">{agent?.name}</span>
          <div
            className="max-w-md rounded p-6 font-normal text-base text-black"
            style={{
              background: '#0000000A',
              whiteSpace: 'pre-wrap',
              borderRadius: !isCurrentUser ? '16px 16px 16px 0px' : '16px 16px 0px 16px',
            }}
          >
            <VStack spacing={4}>
              {isLoading ? (
                <img src="/images/loading.gif" alt="loading" className="w-12 rounded-full" />
              ) : (
                <span>
                  {message} {showCursor && <span className="cursor-blink">|</span>}
                </span>
              )}
            </VStack>
          </div>
        </VStack>
        <img src={agent?.image_url} alt="" className="mb-1 h-12 w-12 rounded-xl" />
      </div>
    </div>
  );
};
