'use client';

import { useListBattle } from '@/api/battles/queries';
import type { IDataBattleDashboard } from '@/api/battles/types';
import NoData from '@/components/NoData';
import HeaderAnonymous from '@/components/no-data/HeaderAnonymous';
import H1 from '@/components/text/H1';
import { TablePagination } from '@/components/ui/table';
import { Show } from '@/components/utilities';
import Container from '@/components/wrapper/Container';
import { useUserLogin } from '@/hooks/useUserLogin';
import { cn } from '@/libs/common';
import type { IPagination } from '@/types';
import { type ReactNode, useState } from 'react';
import CardBattles from '../HomePage/components/AIBattles/CardBattles';

const useBattleData = (status: 'ongoing' | 'upcoming' | 'completed' | 'cancelled', page: number, limit: number) => {
  const { data, isFetching } = useListBattle({
    variables: { page, limit, sort_key: '', sort_type: 'DESC', status },
  });

  return { data, isFetching };
};

const BattlesDashBoard = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(6);

  const { isLoggedIn } = useUserLogin();
  const ongoing = useBattleData('ongoing', page, limit);
  const upcoming = useBattleData('upcoming', page, limit);
  const completed = useBattleData('completed', page, limit);
  const cancelled = useBattleData('cancelled', page, limit);

  const paginations = [
    ongoing?.data?.pagination,
    upcoming?.data?.pagination,
    completed?.data?.pagination,
    cancelled?.data?.pagination,
  ].filter(Boolean) as IPagination[];

  const paginationData = paginations?.reduce(
    (max, current) => {
      return (current.total_item || 0) > (max.total_item || 0) ? current : max;
    },
    { total_item: 0 } as IPagination
  );

  return (
    <Container className="mt-6">
      <Section
        title="ONGOING BATTLES"
        data={ongoing.data?.data}
        isFetching={ongoing.isFetching}
        noData={{
          title: 'Silence Before the Storm',
          paragraph: (
            <>
              The arena is silent. <br /> Check back soon to catch the next <br /> showdown live.{' '}
            </>
          ),
        }}
      />
      <Section
        title="UPCOMING BATTLES"
        data={upcoming.data?.data}
        isFetching={upcoming.isFetching}
        noData={{
          title: 'The Calendar’s Clear',
          paragraph: (
            <>
              Nothing’s scheduled… for now. <br /> Stay tuned, new challengers are lining up.{' '}
            </>
          ),
        }}
      />
      <Section
        title="PREVIOUS BATTLES"
        data={completed.data?.data}
        isFetching={completed.isFetching}
        noData={{
          title: 'No Legends Made — Yet',
          paragraph: (
            <>
              The first epic clash is just <br /> around the corner
            </>
          ),
        }}
      />

      <Show when={isLoggedIn}>
        <Section
          title="CANCELLED BATTLES"
          data={cancelled?.data?.data}
          isFetching={cancelled?.isFetching}
          noData={{
            title: 'No Legends Made — Yet',
            paragraph: (
              <>
                The first epic clash is just <br /> around the corner
              </>
            ),
          }}
        />
      </Show>

      <Show when={(paginationData?.total_page ?? 0) > 1}>
        <div className="sticky bottom-0 border-white/15 border-t py-2 backdrop-blur-sm">
          <TablePagination
            onPageChange={(p) => setPage(p)}
            onPageSizeChange={(l) => {
              setLimit(Number(l));
              setPage(1);
            }}
            pagination={{
              ...paginationData,
              current_page: page,
              limit: limit,
            }}
          />
        </div>
      </Show>
    </Container>
  );
};

const Section = ({
  title,
  data,
  isFetching,
  noData,
}: {
  title: string;
  data: IDataBattleDashboard[] | undefined;
  isFetching: boolean;
  noData: { title: ReactNode; paragraph: ReactNode };
}) => {
  return (
    <div className="mb-20">
      <H1 className="text-[#131313]">{title}</H1>
      <div
        className={cn('my-8 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3', {
          hidden: !data?.length,
        })}
      >
        {data?.map((item, index) => (
          <CardBattles key={index} item={item} />
        ))}
      </div>
      <NoData
        isFetching={isFetching}
        data={data}
        Icon={<HeaderAnonymous />}
        Title={noData.title}
        Paragraph={noData.paragraph}
        className="mt-8"
        classNames={{
          title: 'text-[#131313] no-shadow',
          paragraph: 'text-[#717171]',
        }}
      />
    </div>
  );
};

export default BattlesDashBoard;
