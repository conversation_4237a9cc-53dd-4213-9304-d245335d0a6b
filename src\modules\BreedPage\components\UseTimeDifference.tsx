import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';

const calculateTimeDifference = (createdUtc: string) => {
  const currentUtc = moment.utc();
  const createdUtcMoment = moment.utc(createdUtc);

  // const years = currentUtc.diff(createdUtcMoment, 'years');
  // createdUtcMoment.add(years, 'years');

  // if (years > 0) {
  //   const days = currentUtc.diff(createdUtcMoment, 'days');
  //   return `${years} Years ${days} Days`;
  // }

  const days = currentUtc.diff(createdUtcMoment, 'days');
  createdUtcMoment.add(days, 'days');

  if (days > 0) {
    return `${days} Days`;
  }

  const hours = currentUtc.diff(createdUtcMoment, 'hours');
  createdUtcMoment.add(hours, 'hours');

  if (hours > 0) {
    // const minutes = currentUtc.diff(createdUtcMoment, 'minutes') % 60;
    return `${hours} Hours`;
  }

  const minutes = currentUtc.diff(createdUtcMoment, 'minutes');
  createdUtcMoment.add(minutes, 'minutes');

  if (minutes > 0) {
    // const seconds = currentUtc.diff(createdUtcMoment, 'seconds') % 60;
    return `${minutes} Minutes`;
  }

  const seconds = currentUtc.diff(createdUtcMoment, 'seconds');
  return `${seconds} Seconds`;
};

const useTimeDifference = (created_utc: string) => {
  const [timeDifference, setTimeDifference] = useState<string | null>(null);

  const calculateCreatedTimeDifference = useCallback(() => {
    setTimeDifference(calculateTimeDifference(created_utc));
  }, [created_utc]);

  useEffect(() => {
    calculateCreatedTimeDifference();
  }, [calculateCreatedTimeDifference]);

  return { timeDifference };
};

export default useTimeDifference;
