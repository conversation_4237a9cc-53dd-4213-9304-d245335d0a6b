import { cn } from '@/libs/common';
import Image from 'next/image';
import { HStack } from '../utilities';

const HeaderAnonymous = () => {
  return (
    <HStack pos={'apart'} noWrap className="-space-x-10 w-full items-center justify-center self-stretch">
      <div className="rotate-[7deg]">
        <Image
          src={'/images/battle-anonymous.png'}
          width={136}
          height={136}
          alt="agent1"
          className={cn('h-[136px] w-[136px] rounded-xl border border-white/35 object-cover object-top', {})}
        />
      </div>
      <div className="-rotate-[7deg]">
        <Image
          src={'/images/battle-anonymous.png'}
          width={136}
          height={136}
          alt="agent2"
          className={cn('h-[136px] w-[136px] rounded-xl border border-white/35 object-cover object-top', {})}
        />
      </div>
      <div className="absolute left-1/2 h-[40px] w-[45px] translate-x-1/4">
        <Image src="/images/vs.png" alt="logo" fill sizes="100vh" className="-rotate-[5deg]" />
      </div>
    </HStack>
  );
};

export default HeaderAnonymous;
