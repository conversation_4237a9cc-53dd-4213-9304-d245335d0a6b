import type { ICountdownTimer } from '@/types';
import clsx, { type ClassValue } from 'clsx';
import { format } from 'date-fns';
import { toast } from 'react-toastify';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function onMutateError(error: any) {
  return toast.error(error?.response?.data?.message || error?.message || error?.statusText);
}

export const sleep = async (time: number) => {
  return new Promise<void>((resolve) =>
    setTimeout(() => {
      resolve();
    }, time)
  );
};

/**
 * Format Number With Custom Fraction Precision
 * Can expan with more locale option
 *
 * @param {(string | number | null | undefined)} value The Inputed Number/String
 * @param {{ minFrac?: number; maxFrac?: number }} [params={
 *     maxFrac: undefined,
 *     minFrac: 2,
 *   }] maxFrac is set to undefined to not cause any data loss, minFrac decide how many zeros ex. 0.000 is 3
 * @returns {string} The Inputed Number Formated With Minimum And Maximum Fraction Digits
 */

export const DATETIME_FORMAT = {
  FULL_DATE_TIME: "yyyy-M-dd'T'HH:mm:ss.SSSX",
  DATETIME_SECONDS: 'dd/MM/yyyy HH:mm:ss',
  MONTH_DATE_YEAR: 'LLL dd, y',
  DATE_MONTH_YEAR: 'dd LLL yyyy',
  DATETIME_MINUTES: 'dd/MM/yyyy HH:mm',
  REVERT_DATETIME: 'yyyy-MM-dd HH:mm',
  DATE_TIME: 'yyyy-MM-dd',
  DATE_YEAR: 'dd-MM-yyyy',
  DATE_TIME_SECONDS: 'YYYY-MM-DD HH:mm:ss',
  DATE_TIME_LOCAL: 'yyyy-MM-dd HH:mm:ss',
};

export const formatDate = (date: string | Date, formatString?: string): string => {
  if (!date) return '';

  const formattedDate = format(new Date(date), formatString || DATETIME_FORMAT.MONTH_DATE_YEAR);

  return formattedDate;
};

export const calculateTimeLeft = (targetDate?: Date, currentDate?: Date): ICountdownTimer | null => {
  if (!targetDate || !currentDate) return null;
  const difference = targetDate.getTime() - currentDate.getTime();
  if (difference < 0) return null;
  let timeLeft: ICountdownTimer = { days: 0, hours: 0, minutes: 0, seconds: 0 };
  if (difference > 0) {
    timeLeft = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000),
    };
  }

  return timeLeft;
};
