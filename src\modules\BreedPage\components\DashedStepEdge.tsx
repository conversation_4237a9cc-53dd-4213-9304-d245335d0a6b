import React from 'react';
import type { EdgeProps } from 'reactflow';

const DashedStepEdge: React.FC<EdgeProps> = ({ sourceX, sourceY, targetX, targetY, id }) => {
  const midY = (sourceY + targetY) / 2;
  const radius = 10;

  const isTurningRight = sourceX < targetX;
  const isTurningLeft = sourceX > targetX;

  let edgePath = `M ${sourceX},${sourceY} V ${midY - radius} `;

  if (isTurningRight) {
    edgePath += `Q ${sourceX},${midY} ${sourceX + radius},${midY} `;
  } else if (isTurningLeft) {
    edgePath += `Q ${sourceX},${midY} ${sourceX - radius},${midY} `;
  }

  edgePath += `H ${targetX > sourceX ? targetX - radius : targetX + radius} `;

  if (isTurningRight) {
    edgePath += `Q ${targetX},${midY} ${targetX},${midY + radius} `;
  } else if (isTurningLeft) {
    edgePath += `Q ${targetX},${midY} ${targetX},${midY + radius} `;
  }

  edgePath += `V ${targetY}`;

  return (
    <>
      <defs>
        <marker id={`arrow-${id}`} markerWidth="10" markerHeight="8" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
          <path d="M 0,0 L 6,3 L 0,6" fill="white" stroke="white" strokeWidth="1" />
        </marker>
      </defs>

      <path d={edgePath} stroke="white" strokeWidth="1" fill="none" strokeDasharray="2 4" markerEnd={`url(#arrow-${id})`} />
    </>
  );
};

export default DashedStepEdge;
