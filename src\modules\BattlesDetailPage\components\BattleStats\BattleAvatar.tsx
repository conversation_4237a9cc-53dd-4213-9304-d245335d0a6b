import { useJudgeComment } from '@/api/battles/queries';
import type { IJudgeComment } from '@/api/battles/types';
import H2 from '@/components/text/H2';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface BattleAvatarProps {
  imageSrc: string;
  agentId?: string;
  totalBets: number | string;
  battles: string | number;
  name: string;
  result?: 'win' | 'lose';
  judges?: any;
}

const Stat = ({ label, value, judges }: { label: string; value: React.ReactNode; judges?: IJudgeComment[] }) => {
  return (
    <VStack spacing={0} className="py-1">
      <Show when={judges && judges?.length > 0}>
        <HStack noWrap spacing={8} className="mb-1">
          {judges?.map((i, z) => (
            <Tooltip label={i?.name} key={z}>
              <div className="h-6 w-6 cursor-pointer rounded-sm hover:opacity-50">
                <Link href={`/agents/${i?.agent_id}?type=JUDGE_AGENT`}>
                  <Image src={i?.image_url} alt={i?.name} className="h-full w-full rounded-sm object-cover" width={100} height={100} />
                </Link>
              </div>
            </Tooltip>
          ))}

          <div className="whitespace-nowrap text-secondary-100 text-xs">{judges?.length} Votes</div>
        </HStack>
      </Show>

      <div className="flex items-center gap-1">
        <p className="whitespace-nowrap text-center font-medium text-white text-xs">{label}</p>
        <p className="text-center font-geist-mono font-semibold text-sm text-white">{value}</p>
      </div>
    </VStack>
  );
};

const BattleAvatar: React.FC<BattleAvatarProps> = ({ result, agentId, imageSrc, totalBets, name }) => {
  const { id } = useParams();

  const { data: dataJudgeComment } = useJudgeComment({
    variables: String(id),
    onError: onMutateError,
    enabled: Boolean(id && result),
  });

  const judges = dataJudgeComment?.filter((x) => String(x.voted_agent_id) === String(agentId));

  return (
    <div className="relative flex-1">
      <div className="relative flex justify-center overflow-hidden rounded-[24px]">
        <Image
          className={cn('h-[180px] w-[180px] rounded-[24px] object-cover', {
            grayscale: result === 'lose',
          })}
          src={imageSrc || '/images/no-image-gray-scale.png'}
          alt="Avatar"
          width={180}
          height={180}
          placeholder="blur"
          blurDataURL="/images/no-image-gray-scale.png"
        />

        {/* {result === 'lose' && <div className="absolute inset-0 z-[6] bg-[#50505068]"></div>} */}

        {result && (
          <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-10 font-jersey15 text-5xl">
            <Image
              src={result === 'win' ? '/images/win.png' : '/images/lose.png'}
              alt=""
              width={result === 'win' ? 95 : 59}
              height={48}
              className="object-cover"
            />
          </div>
        )}
      </div>

      <H2 className="mt-3 mb-1 text-center font-jersey15 font-normal text-3xl text-[#FFFFFF] uppercase lg:text-5xl">{name}</H2>

      <div className="text-center font-semibold text-[#FFFFFF] text-sm uppercase">Total bets: {totalBets}</div>

      <Show when={!!result}>
        <VStack className="mt-2" spacing={0}>
          <div className="text-center font-semibold text-[#FFFFFF] text-sm uppercase">Votes</div>

          <HStack spacing={8} pos="center" className="h-8">
            {judges?.map((i, z) => (
              <Tooltip label={i?.name} key={z}>
                <div className="h-6 w-6 cursor-pointer rounded-sm hover:opacity-50">
                  <Link href={`/agents/${i?.agent_id}?type=JUDGE_AGENT`} target="_blank">
                    <Image src={i?.image_url} alt={i?.name} className="h-full w-full rounded-sm object-cover" width={100} height={100} />
                  </Link>
                </div>
              </Tooltip>
            ))}
          </HStack>
        </VStack>
      </Show>
    </div>
  );
};

export default BattleAvatar;
