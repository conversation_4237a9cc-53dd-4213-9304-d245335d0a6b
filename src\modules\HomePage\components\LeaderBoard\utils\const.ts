export const data_tabs = [
  { label: 'All', value: '' },
  { label: 'Participation', value: 'battle_count' },
  { label: 'Bet Volume', value: 'bet_volume' },
];

export const headersTable = [
  { name: 'RANK', key: 'index', isSort: false, className: 'w-[52px]' },
  { name: 'PLAY<PERSON>', key: 'username', isAvatar: true, className: 'max-w-[300px] w-full' },
  {
    name: 'BATTLES',
    key: 'battle_count',
    isInfo: true,
    isSort: true,
    contentInfo: 'Total number of battles a user has participated in.',
    className: 'w-[85px]',
  },
  {
    name: 'BETS',
    key: 'bet_count',
    isInfo: true,
    isSort: true,
    contentInfo: 'Total number of bets a user has placed.',
    className: 'w-[75px]',
  },
  {
    name: 'BET VOLUME',
    key: 'bet_volume',
    isInfo: true,
    isSort: true,
    contentInfo: 'Total amount of money a user has bet on the platform.',
    className: 'text-right flex justify-end w-[180px]',
  },
  {
    name: 'TOTAL PAYOUT',
    key: 'total_payout',
    isInfo: true,
    isSort: true,
    contentInfo: 'Reward received when the user wins a bet.',
    className: 'text-right justify-end w-[180px]',
  },
];
