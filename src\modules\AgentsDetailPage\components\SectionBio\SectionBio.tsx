import type { ICriteriaAgent, ICriteriaJudge } from '@/api/agent/types';
import { Icons } from '@/assets/icons';
import { HStack } from '@/components/utilities';
import { PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart } from 'recharts';
import WrapperSectionAgentDetail from '../WrapperSectionAgentDetail';

type Props = {
  description: string;
  criteria: ICriteriaAgent | ICriteriaJudge;
  agentType: string;
};

const MappingTitleAgent: Record<keyof ICriteriaAgent, string[]> = {
  charisma_and_presence: ['CHARISMA & PRESENCE'],
  logic_and_reasoning: ['LOGIC &', 'REASONING'],
  vision_and_innovation: ['VISION &', 'INNOVATION'],
  emotional_impact: ['EMOTIONAL IMPACT'],
  tactical_skills: ['TACTICAL', 'SKILLS'],
  credibility_and_execution: ['CREDIBILITY &', 'EXECUTION'],
};

const MappingTitleJudge: Record<keyof ICriteriaJudge, string[]> = {
  objectivity: ['Objectivity'],
  logical_analysis: ['Logical', 'Analysis'],
  ethical_sensitivity: ['Ethical', ' Sensitivity'],
  creativity_appreciation: ['Creativity', ' Appreciation'],
  practicality_feasibility: ['Practicality /', 'Feasibility'],
  audience_empathy: ['Audience', ' Empathy'],
};

const SectionBio = (props: Props) => {
  const { description, criteria, agentType } = props;
  const MappingTitle = agentType === 'JUDGE_AGENT' ? MappingTitleJudge : MappingTitleAgent;
  const orderedKeys = Object.keys(MappingTitle) as Array<keyof ICriteriaAgent>;

  const data = (Object.keys(criteria) as Array<keyof ICriteriaAgent>)
    .sort((a, b) => orderedKeys.indexOf(a) - orderedKeys.indexOf(b))
    .map((item) => {
      return {
        criteria: item,
        value: (criteria as any)?.[item],
      };
    });

  return (
    <WrapperSectionAgentDetail title="Bio" icon={<Icons.bio />}>
      <p className="font-medium text-[#131313] text-sm uppercase not-italic leading-[150%] lg:text-base">{description}</p>
      <HStack className="mt-8 justify-center">
        <RadarChart outerRadius={'70%'} width={450} height={400} data={data} margin={{ top: 30, right: 30, bottom: 30, left: 30 }}>
          <PolarGrid stroke="#E7E7E7" />
          <PolarAngleAxis
            stroke="#E7E7E7"
            dataKey="criteria"
            tick={(props) => {
              const { x, y, payload, textAnchor, cx, cy } = props;

              const label = payload.value;
              const matching = data.find((item) => item.criteria === label);

              const lines = (MappingTitle as any)?.[payload.value];

              const angle = Math.atan2(cy - y, x - cx);

              const offset = 6;
              const newX = x + Math.cos(angle) * offset;
              const newY = y - Math.sin(angle) * offset;

              return (
                <text x={newX} y={newY} textAnchor={textAnchor} fill="#131313" fontSize="12" fontWeight="500" fontFamily="fontGeistMono">
                  {lines?.map((line: string, index: number) => (
                    <tspan x={x} dy={index === 0 ? 0 : 14} key={index}>
                      {line}
                    </tspan>
                  ))}
                  ({matching?.value})
                </text>
              );
            }}
          />
          <PolarRadiusAxis stroke="#E7E7E7" display={'none'} domain={[0, 100]} />
          <Radar name="criteria" dataKey="value" stroke="#FF3901" fill="rgba(255, 235, 0, 0.5)" fillOpacity={0.6} />
        </RadarChart>
      </HStack>
    </WrapperSectionAgentDetail>
  );
};

export default SectionBio;
