import { define<PERSON>hain } from 'viem';
import { createConfig, http } from 'wagmi';
import { metaMask, walletConnect } from 'wagmi/connectors';
import { env } from '../const';

export const customChain = defineChain({
  id: env.CHAIN_ID,
  name: '0G',
  network: 'custom',
  nativeCurrency: {
    name: 'OG Token',
    symbol: '0G',
    decimals: 18,
  },
  rpcUrls: {
    default: { http: [env.CHAIN_RPC_URL] },
  },
  blockExplorers: {
    default: { name: 'Custom Explorer', url: 'https://chainscan-galileo.0g.ai/' },
  },
  testnet: true,
});

export const configWagmi = createConfig({
  chains: [customChain],
  transports: {
    [customChain.id]: http(),
  },
  connectors: [
    metaMask(),
    walletConnect({
      projectId: '583eb2be6bb33f7274601184a573cb40',
      showQrModal: true,
      metadata: {
        name: 'My App',
        description: 'My dApp description',
        url: 'http://localhost:3000/',
        icons: ['http://localhost:3000/logo.svg'],
      },
      qrModalOptions: {},
    }),
  ],
  ssr: false,
  multiInjectedProviderDiscovery: false,
});

declare module 'wagmi' {
  interface Register {
    config: typeof configWagmi;
  }
}
