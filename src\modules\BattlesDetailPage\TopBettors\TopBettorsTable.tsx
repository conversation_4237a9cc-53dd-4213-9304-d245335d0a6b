import type { Bet, IResponsiveTopBattors } from '@/api/battles/types';
import { Icons } from '@/assets/icons';
import NoData from '@/components/NoData';
import { Table, TableBody, TableCell, TableHeader, TableRow, TableSkeleton } from '@/components/ui/table';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import { Fragment } from 'react';

const TopBettorsTable = ({ data, isFetching }: { data: IResponsiveTopBattors | undefined; isFetching: boolean }) => {
  const userRankInfo = data?.current_user_data;
  return (
    <div className="h-[700px] overflow-auto">
      <Show when={!data || data?.data?.length === 0}>
        <NoData
          isFetching={isFetching}
          data={data?.data}
          Icon={<Icons.crown />}
          Title={
            <>
              No One’s Claimed <br /> the Crown Yet
            </>
          }
          Paragraph={
            <>
              The crowd’s still placing their first <br /> bets.Will you be the one to start the <br /> leaderboard buzz?
            </>
          }
          className="mt-3"
        />
      </Show>

      <Show when={!!data?.data?.length || isFetching}>
        <Table>
          <TableHeader>
            <TableRow className="whitespace-nowrap font-geistMono font-medium text-dark-600 text-sm">
              <TableCell className="px-0">RANK</TableCell>
              <TableCell className="px-0">PLAYER</TableCell>
              <TableCell className="px-0">BET VOLUME</TableCell>
            </TableRow>
          </TableHeader>

          <TableBody>
            <Show when={Boolean(data?.data)}>
              {data?.data?.map((item, index) => (
                <Fragment key={index}>
                  <Show when={Number(userRankInfo?.rank || 0) === index + 1}>
                    <TableRowCurrentRank item={userRankInfo} />
                  </Show>
                  <Show when={Number(userRankInfo?.rank || 0) !== index + 1}>
                    <TableRow key={index} className={cn('border-[#D0D0D0] border-b text-dark-100')}>
                      <TableCell className="whitespace-nowrap px-4 font-geistMono text-sm">{item?.rank}</TableCell>
                      <TableCell>
                        <HStack spacing={12} noWrap>
                          <div className="h-7 w-7">
                            <Image src={item?.user_avatar ?? ''} alt="" width={32} height={32} className="h-7 w-7 rounded-md" />
                          </div>
                          <VStack className="font-geistMono font-medium text-sm uppercase" spacing={4}>
                            {item?.user_username}
                            <span className="whitespace-nowrap text-dark-600 text-xs">{item?.agent_name}</span>
                          </VStack>
                        </HStack>
                      </TableCell>
                      <TableCell className="whitespace-nowrap text-center font-geistMono">
                        {formatNumber(Number(item?.total_bet_amount))}
                      </TableCell>
                    </TableRow>
                  </Show>
                </Fragment>
              ))}
            </Show>

            <Show when={isFetching && !data?.data}>
              <TableSkeleton loading={isFetching} col={3} />
            </Show>

            {/* <Show when={!isFetching && (!data || data?.data?.length === 0)}>
              <TableEmptyData col={3} />
            </Show> */}
          </TableBody>
        </Table>
      </Show>
    </div>
  );
};

export default TopBettorsTable;

const TableRowCurrentRank = ({ item }: { item: Bet | undefined }) => {
  if (!item) {
    return null;
  }
  return (
    <TableRow key={item.rank} className="overflow-hidden rounded-[8px] bg-[#FFEB00]">
      <TableCell className={cn('whitespace-nowrap rounded-tl-[8px] rounded-bl-[8px] px-4 font-geistMono text-sm')}>{item?.rank}</TableCell>
      <TableCell>
        <HStack spacing={12} noWrap>
          <div className="h-7 w-7">
            <Image src={item?.user_avatar ?? ''} alt="" width={32} height={32} className="h-7 w-7 rounded-md" />
          </div>
          <VStack className="font-geistMono font-medium text-sm uppercase" spacing={4}>
            {item?.user_username}
            <span className="whitespace-nowrap text-dark-600 text-xs">{item?.agent_name}</span>
          </VStack>
        </HStack>
      </TableCell>
      <TableCell className={cn('whitespace-nowrap rounded-tr-[8px] rounded-br-[8px] px-4 text-center font-geistMono text-sm')}>
        {formatNumber(Number(item?.total_bet_amount))}
      </TableCell>
    </TableRow>
  );
};
