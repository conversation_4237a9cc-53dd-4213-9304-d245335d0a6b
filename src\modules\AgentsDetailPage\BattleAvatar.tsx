import { HStack } from '@/components/utilities';
import { cn } from '@/libs/utils';
import Image from 'next/image';
import { color_status } from './utils/const';

export const BattleAvatar: React.FC<{
  imageSrc: string;
  status: string;
  name: string;
  redirect?: string;
}> = ({ imageSrc, status, name, redirect }) => {
  return (
    <div
      className={cn('relative h-[186px] w-[193px] rounded-lg', redirect && 'cursor-pointer hover:opacity-50')}
      onClick={() => {
        if (redirect) window.open(redirect, '_blank');
        else return;
      }}
    >
      <Image
        width={193}
        height={186}
        className="absolute inset-0 h-full w-full rounded-lg bg-cover object-cover"
        src={imageSrc || '/images/no-image.png'}
        alt="Avatar"
      />

      <div className="relative h-full w-full overflow-hidden rounded-lg">
        <div className="absolute inset-0 h-full">
          <div className="absolute inset-2 h-[90%]">
            <div className="absolute inset-1 before:absolute before:inset-0 before:border before:border-white before:border-t-0 before:opacity-30" />
            <div
              className="absolute inset-0 h-full border border-white/0 border-t-0"
              style={{
                maskImage:
                  'linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.6) 50%, white 100%)',
                WebkitMaskImage:
                  'linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.6) 50%, white 100%)',
              }}
            />
            <p className="-translate-x-1/2 absolute top-2 left-1/2 z-10 transform text-center font-geist-mono font-semibold text-secondary-50 text-xs">
              {name}
            </p>
            <HStack
              spacing={16}
              noWrap
              className={`-translate-x-1/2 absolute bottom-[-7px] left-1/2 z-10 transform rounded-sm ${color_status(status)} p-1 font-semibold text-amaranth-50`}
            >
              <p className={`text-center font-medium text-sm`}>{status}</p>
            </HStack>
          </div>
        </div>
        <div className="absolute inset-0 inset-y-1/2 bottom-0 h-[200px] bg-[url('/net.png')] bg-cover opacity-70"></div>
        <div
          style={{
            background: 'linear-gradient(180deg, #040201 0%, rgba(4, 2, 1, 0) 100%)',
          }}
          className="absolute top-0 left-0 h-[60px] w-full rounded-lg"
        />
      </div>
    </div>
  );
};
