import Footer from '@/layouts/MainLayout/Footer';
import Header from '@/layouts/MainLayout/Header';

type LayoutProps = Readonly<{ children: React.ReactNode }>;

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="flex min-h-screen flex-col overflow-clip">
      <Header />
      <main className="mx-auto w-full max-w-[1440px] flex-1">{children}</main>
      <div className="hidden md:block">
        <Footer />
      </div>
    </div>
  );
};

export default Layout;
