'use client';

import { useMeQuery } from '@/api/auth/queries';
import { useUserStore } from '@/stores/UserStore';
import { deleteCookie, getCookie } from 'cookies-next';
import { useEffect } from 'react';
import { useAccount, useDisconnect } from 'wagmi';

export const useUserLogin = () => {
  const { setUser, logout } = useUserStore();
  const { address, isConnecting, status, isConnected } = useAccount();
  const { disconnect } = useDisconnect();

  const accessToken = getCookie('access_token');

  const { data, refetch, ...rest } = useMeQuery({
    onSuccess: (user) => {
      if (user) {
        setUser(user);
      } else {
        disconnect();
      }
    },
    onError: () => {
      deleteCookie('access_token');
      deleteCookie('refresh_token');
      logout();
      disconnect();
    },
    enabled: Boolean(address && !isConnecting && accessToken),
  });

  useEffect(() => {
    if (!accessToken && disconnect) disconnect();
  }, [accessToken, disconnect]);

  useEffect(() => {
    if (!address && data) {
      logout();
    }
  }, [address, data, logout]);

  return {
    isLoggedIn: Boolean(data && isConnected),
    user: data,
    address,
    refetch,
    ...rest,
  };
};
