import { getListLiveBattle } from '@/api/battles/requests';
import type { IBattleDetail, IDataLiveBattle, ILiveBattleQuery } from '@/api/battles/types';
import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { Show, VStack } from '@/components/utilities';
import { socket } from '@/config/socket';
import { cn, onMutateError } from '@/libs/common';
import { useInfiniteQuery } from '@tanstack/react-query';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { useParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import BattleFinished from './BattleFinished';
import { MessageBubble } from './MessageBubble';

const MessageComponent = ({ data, remainingTime }: { data: IBattleDetail | undefined; remainingTime: string }) => {
  const { id } = useParams();
  const [paramsQuery] = useState<Partial<ILiveBattleQuery>>({
    limit: 30,
    page: 1,
    sort_type: 'DESC',
  });
  const { ref, inView } = useInView({
    rootMargin: '0px 0px 0px 0px',
    root: null,
    threshold: 0,
  });

  const [displayedMessages, setDisplayedMessages] = useState<IDataLiveBattle[]>([]);
  const [currentTypedMessage, setCurrentTypedMessage] = useState<string>('');
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isLoadingMessage, setIsLoadingMessage] = useState(false);
  const [dataSocket, setDataSocket] = useState<IDataLiveBattle>();
  const [contentEnd, setContentEnd] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [scrollEl, setScrollEl] = useState<HTMLDivElement | null>(null);
  const [isAtBottomState, setIsAtBottomState] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const {
    data: dataLiveBattle,
    isFetchingNextPage,
    isFetching,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ['api/live-battle', paramsQuery, id],
    queryFn: ({ pageParam = 1 }) =>
      getListLiveBattle({
        ...paramsQuery,
        page: pageParam,
        battle_id: String(id),
      }),
    getNextPageParam: (lastPage) => {
      if (Number(lastPage?.pagination?.page) >= Number(lastPage?.pagination?.total_page)) return undefined;
      return Number(lastPage?.pagination?.page) + 1;
    },
    onError: onMutateError,
    refetchOnMount: true,
    enabled: !!id,
  });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  const isAtBottom = () => {
    if (messagesEndRef.current) {
      return messagesEndRef.current.scrollHeight - messagesEndRef.current.scrollTop <= messagesEndRef.current.clientHeight + 10;
    }
    return false;
  };

  const simulateTypingEffect = (text: string): Promise<string> => {
    return new Promise((resolve) => {
      let currentText = '';
      let index = 0;
      const delay = 80;

      const typeNextChar = () => {
        const nowVisible = document.visibilityState === 'visible';

        if (!nowVisible) {
          setCurrentTypedMessage(text);
          resolve(text);
          return;
        }

        if (index < text.length) {
          currentText += text[index];
          setCurrentTypedMessage(currentText);
          index++;

          setTimeout(typeNextChar, delay);

          if (isAtBottom()) {
            requestAnimationFrame(() => scrollToBottom());
          }
        } else {
          resolve(currentText);
        }
      };

      typeNextChar();
    });
  };

  useEffect(() => {
    const onVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        scrollToBottom();
      }
    };

    document.addEventListener('visibilitychange', onVisibilityChange);
    return () => document.removeEventListener('visibilitychange', onVisibilityChange);
  }, []);

  useEffect(() => {
    if (dataLiveBattle) {
      const allMsgs = dataLiveBattle.pages.flatMap((group) => group.data);

      setDisplayedMessages((prev) => [...prev, ...allMsgs]);
    }
  }, [dataLiveBattle]);

  useEffect(() => {
    if (!id) return;

    const isEndCountRef = { current: 0 };

    const handleLiveBattle = async (newMessage: IDataLiveBattle) => {
      if (!newMessage) return;

      setDataSocket(newMessage);
      setCurrentTypedMessage('');
      const wasAtBottom = isAtBottom();
      if (wasAtBottom) {
        requestAnimationFrame(() => {
          setTimeout(() => {
            scrollToBottom();
          }, 50);
        });
      }
      setIsLoadingMessage(true);

      await new Promise((resolve) => setTimeout(resolve, 3000));
      setIsLoadingMessage(false);
      const typedText = await simulateTypingEffect(newMessage.content);
      setDisplayedMessages((prev) => {
        if (newMessage?.is_end) {
          isEndCountRef.current += 1;
          if (isEndCountRef.current === 2) {
            setContentEnd(true);
          }
        }
        return [{ ...newMessage, content: typedText }, ...prev];
      });

      setCurrentTypedMessage('');
    };

    socket.on(`live_battle_${id}`, handleLiveBattle);
    return () => {
      socket.off(`live_battle_${id}`, handleLiveBattle);
    };
  }, [id, socket]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      requestAnimationFrame(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollTo({
            top: messagesEndRef.current.scrollHeight,
            behavior: 'smooth',
          });
        }
      });
    }
  };

  useEffect(() => {
    return () => {
      if (typingIntervalRef.current) {
        clearTimeout(typingIntervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!scrollEl) return;

    const handleScroll = () => {
      const isAtBottom = scrollEl.scrollTop === 0;
      setShowScrollButton(!isAtBottom);
      setIsAtBottomState(isAtBottom);
    };

    scrollEl.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => {
      scrollEl.removeEventListener('scroll', handleScroll);
    };
  }, [scrollEl, displayedMessages.length]);

  const handleScrollBottom = () => {
    if (scrollEl) {
      scrollEl.scrollTo({
        top: scrollEl.scrollHeight,
        behavior: 'smooth',
      });
      setShowScrollButton(false);
    }
  };

  const handleScrollTop = () => {
    if (scrollEl) {
      scrollEl.scrollTo({
        top: -scrollEl.scrollHeight,
        behavior: 'smooth',
      });
      setIsAtBottomState(false);
    }
  };

  const isBattleFinished = data?.winner?.id && data?.resolved_onchain_status === 'Completed';

  return (
    <>
      <VStack spacing={24} className="relative pr-3 font-metropolis" ref={messagesEndRef}>
        <VStack className="max-h-[600px] flex-col-reverse overflow-y-auto px-4" spacing={24} ref={setScrollEl}>
          {isBattleFinished && <BattleFinished data={data} />}
          {currentTypedMessage && (
            <MessageBubble
              agent={dataSocket?.agent}
              message={currentTypedMessage}
              isCurrentUser={data?.agent1?.name !== dataSocket?.agent?.name}
              showCursor={true}
            />
          )}
          {isLoadingMessage && (
            <MessageBubble agent={dataSocket?.agent} isLoading isCurrentUser={data?.agent1?.name !== dataSocket?.agent?.name} />
          )}
          {displayedMessages.map((msg, index) => (
            <MessageBubble key={index} agent={msg.agent} message={msg.content} isCurrentUser={data?.agent1?.name !== msg?.agent?.name} />
          ))}

          <div ref={ref} className="mb-5 flex justify-center" style={{ minHeight: 40 }}>
            {isFetching && <Icons.loading className="animate-spin" />}
          </div>
        </VStack>
        <Show when={showScrollButton}>
          <Button
            variant={'outline'}
            className={cn(
              'absolute right-8 bottom-10 rounded-lg border-2 border-dark-100 bg-white shadow-none hover:border-[#FF3901] hover:bg-[#FFEB00] hover:text-[#FF3901]'
            )}
            onClick={handleScrollBottom}
          >
            <ArrowDown color="#131313" />
          </Button>
        </Show>
        <Show when={isAtBottomState}>
          <Button
            variant={'outline'}
            className={cn(
              'absolute right-8 bottom-10 rounded-lg border-2 border-dark-100 bg-white shadow-none hover:border-[#FF3901] hover:bg-[#FFEB00] hover:text-[#FF3901]'
            )}
            onClick={handleScrollTop}
          >
            <ArrowUp color="#131313" />
          </Button>
        </Show>
        {displayedMessages.length === 0 && !dataSocket && data?.status !== 'completed' && remainingTime !== 'Time over' && (
          <>
            <MessageBubble agent={data?.agent1} isLoading isCurrentUser={false} />
            <MessageBubble agent={data?.agent2} isLoading isCurrentUser={true} />
          </>
        )}
      </VStack>
      {(contentEnd || dataLiveBattle?.pages[0]?.data[0]?.is_end) && data?.status !== 'completed' && remainingTime !== 'Time over' && (
        <div className="mx-auto mt-2 flex w-fit items-center gap-2 rounded-lg bg-[#E06D381A] px-3 py-2 text-center font-medium text-red-damask-500 text-sm">
          <Icons.alert />
          {data?.agent1?.name} and {data?.agent2?.name} have made all their points.
        </div>
      )}
    </>
  );
};

export default MessageComponent;
