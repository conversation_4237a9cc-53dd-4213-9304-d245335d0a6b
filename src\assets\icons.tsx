import type { ForwardRefExoticComponent, RefAttributes, SVGProps } from 'react';

import alert from './svg/alert.svg';
import alignJustify from './svg/align-justify.svg';
import arrowRight from './svg/arrowRight.svg';
import battle from './svg/battle.svg';
import bio from './svg/bio.svg';
import boltSlash from './svg/bolt-slash.svg';
import breedAttachment from './svg/breed-attachment.svg';
import breedX from './svg/breed-x.svg';
import bullseye from './svg/bullseye.svg';
import calendar from './svg/calendar.svg';
import caretDown from './svg/caret-down.svg';
import chevronDown from './svg/chevron-down.svg';
import closeEye from './svg/close-eye.svg';
import close from './svg/close.svg';
import copy from './svg/copy.svg';
import copyright from './svg/copyright.svg';
import crown from './svg/crown.svg';
import cube from './svg/cube.svg';
import done from './svg/done.svg';
import edit from './svg/edit.svg';
import evaluation from './svg/evaluation.svg';
import faceChill from './svg/face-chill.svg';
import firstStage from './svg/first-stage.svg';
import github from './svg/github.svg';
import heart from './svg/heart.svg';
import info from './svg/info.svg';
import link from './svg/link.svg';
import loading from './svg/loading.svg';
import lockOpen from './svg/lock-open.svg';
import logoFull from './svg/logo-full.svg';
import logoNoDes from './svg/logo-no-des.svg';
import logo from './svg/logo.svg';
import logout from './svg/logout.svg';
import maximizeWhite from './svg/maximize-white.svg';
import maximize from './svg/maximize.svg';
import medal from './svg/medal.svg';
import nativeToken from './svg/nativeToken.svg';
import noDataTable from './svg/no-data-table.svg';
import noMessage from './svg/no-message.svg';
import openEye from './svg/open-eye.svg';
import reaction from './svg/reaction.svg';
import save from './svg/save.svg';
import search from './svg/search.svg';
import secondStage from './svg/second-stage.svg';
import sort from './svg/sort.svg';
import spin from './svg/spin.svg';
import star from './svg/star.svg';
import strength from './svg/strength.svg';
import structure from './svg/structure.svg';
import sword from './svg/sword.svg';
import television from './svg/television.svg';
import television2 from './svg/television2.svg';
import thirdStage from './svg/third-stage.svg';
import twitter from './svg/twitter.svg';
import userCircle from './svg/user-circle.svg';
import user from './svg/user.svg';
import wallet from './svg/wallet.svg';
import warning from './svg/warning.svg';
import watchRed from './svg/watch-red.svg';
import watch from './svg/watch.svg';
import weakness from './svg/weakness.svg';
import xCircle from './svg/x-circle.svg';
import X from './svg/x.svg';
import debate from './svg/debate.svg';

const IconList = {
  debate,
  boltSlash,
  noMessage,
  crown,
  copyright,
  firstStage,
  secondStage,
  thirdStage,
  done,
  television,
  battle,
  heart,
  television2,
  chevronDown,
  copy,
  alert,
  breedX,
  caretDown,
  breedAttachment,
  cube,
  nativeToken,
  watchRed,
  save,
  watch,
  loading,
  info,
  github,
  reaction,
  warning,
  faceChill,
  twitter,
  arrowRight,
  maximize,
  edit,
  X,
  logo,
  logoFull,
  logoNoDes,
  maximizeWhite,
  close,
  user,
  lockOpen,
  openEye,
  closeEye,
  search,
  noDataTable,
  star,
  userCircle,
  logout,
  spin,
  alignJustify,
  calendar,
  xCircle,
  wallet,
  sort,
  sword,
  medal,
  bullseye,
  bio,
  evaluation,
  strength,
  weakness,
  link,
  structure,
};

type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;
type ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;
interface IconProps extends ComponentAttributes {
  size?: string | number;
  absoluteStrokeWidth?: boolean;
}

export type Icon = ForwardRefExoticComponent<IconProps>;

export const Icons = IconList as Record<keyof typeof IconList, Icon>;
