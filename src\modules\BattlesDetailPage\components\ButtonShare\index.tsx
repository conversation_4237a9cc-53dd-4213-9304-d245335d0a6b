'use client';

import type { IBattleDetail } from '@/api/battles/types';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { Dialog, DialogOverlay, DialogPortal, DialogTrigger } from '@/components/ui/dialog';
import { HStack, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import type { FCC } from '@/types';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import * as htmlToImage from 'html-to-image';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import slugify from 'slugify';
import { getBase64FromUrl } from './libs';

type Props = {
  topBar?: React.ReactNode;
  bottomBar: React.ReactNode;
} & Partial<IBattleDetail>;

const ButtonShare: FCC<Props> = ({ topBar, bottomBar, banner_url, topic, agent1, agent2, winner, winner_id, children }) => {
  const [open, setOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [url, setUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const handleDownload = async () => {
    if (!topic || !url) return;
    try {
      const title = slugify(topic, {
        replacement: '-',
        remove: undefined,
        lower: true,
        strict: false,
        locale: 'en',
        trim: true,
      });

      const link = document.createElement('a');
      link.href = url;
      link.download = `${title}.png`;
      link.click();
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  const handleCopy = async () => {
    if (!url) return;

    try {
      const res = await fetch(url);
      const blob = await res.blob();

      const item = new ClipboardItem({ 'image/png': blob });
      await navigator.clipboard.write([item]);
      toast.success('Copied image to clipboard!');
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  useEffect(() => {
    if (!open) return;
    const getImageUrl = async () => {
      if (!contentRef.current) return;
      setLoading(true);
      const clonedNode = contentRef.current.cloneNode(true) as HTMLElement;

      const images = clonedNode.querySelectorAll('img');
      for (const img of Array.from(images)) {
        if (!img.src.startsWith('data:image')) {
          try {
            img.src = (await getBase64FromUrl(img.src)) as string;
          } catch (err) {
            console.error('Image to base64 failed:', err);
          }
        }
      }

      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.appendChild(clonedNode);
      document.body.appendChild(container);

      try {
        const dataUrl = await htmlToImage.toPng(clonedNode, {
          pixelRatio: window.devicePixelRatio ? window.devicePixelRatio : 2,
          cacheBust: true,
          backgroundColor: undefined,
        });

        setUrl(dataUrl);
      } catch (error: any) {
        console.log(error);
      } finally {
        container.remove();
        setLoading(false);
      }
    };
    const frame = requestAnimationFrame(() => {
      getImageUrl();
    });

    return () => cancelAnimationFrame(frame);
  }, [contentRef.current, open]);

  return (
    <div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>

        <DialogPortal>
          <DialogOverlay className={cn('bg-[#0427FACC]')} />
          <DialogPrimitive.Content
            className={cn(
              'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-[520px] translate-x-[-50%] translate-y-[-50%] gap-4 px-5 py-4 duration-200 data-[state=closed]:animate-out data-[state=open]:animate-in sm:rounded-xl md:max-w-[620px] lg:max-w-[800px]'
            )}
          >
            <HStack pos="center">{topBar}</HStack>

            <div className="!bg-transparent relative h-fit rounded-[35px]" ref={contentRef}>
              <div className="-z-10 absolute inset-1 rounded-[34px] bg-[#FAE9DA]"></div>

              <img src={banner_url || '/images/no-image.png'} alt="" width={800} height={450} className="aspect-[800/450] rounded-[27px]" />

              {/* <div className="absolute inset-0"></div> */}

              <div className="absolute inset-3 flex h-fit items-center justify-between lg:inset-6">
                <img
                  src="/images/logo-header.png"
                  alt="logo"
                  width={90}
                  height={39}
                  className="-rotate-[5deg] h-auto w-[60px] lg:w-[90px]"
                />

                <div
                  id="share-app-title"
                  className="flex items-center rounded-md bg-[#FFFFFF] px-2 py-0.5 font-semibold text-[#131313] text-[8px] uppercase leading-none lg:py-1 lg:text-xxs"
                >
                  <span>battleofagents.ai</span>
                </div>
              </div>

              <VStack spacing={0} align="center" className="absolute right-10 bottom-3 left-10 md:bottom-5 lg:bottom-[30px] ">
                <HStack
                  pos={'apart'}
                  noWrap
                  className={cn(
                    ' w-full items-center justify-center self-stretch',
                    winner ? 'space-x-4 lg:space-x-6' : '-space-x-4 lg:-space-x-8'
                  )}
                >
                  <div className={cn('relative w-fit', !winner && '-rotate-[7deg]')}>
                    <img
                      src={agent1?.image_url || '/images/no-image.png'}
                      width={136}
                      height={136}
                      alt="agent1"
                      className={cn(
                        'w-[46px] origin-right rounded-md object-cover object-top transition-all duration-200 group-hover:scale-125 md:w-[80px] lg:h-[110px] lg:w-[110px]',
                        {
                          ' grayscale': winner_id && winner_id === agent2?.id && !!winner?.id,
                        }
                      )}
                    />
                    {winner && (
                      <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
                        <img
                          src={agent1?.id === winner?.id ? '/images/win.png' : '/images/lose.png'}
                          className={agent1?.id === winner?.id ? 'w-[44px] lg:w-[54px]' : 'w-[24px] lg:w-[34px]'}
                        />
                      </div>
                    )}
                  </div>

                  {winner && <img src="/images/vs.png" alt="logo" className="w-6 lg:w-10" />}

                  <div className={cn('relative', !winner && ' rotate-[7deg]')}>
                    <img
                      src={agent2?.image_url || '/images/no-image.png'}
                      width={136}
                      height={136}
                      alt="agent2"
                      className={cn(
                        'w-[46px] origin-left rounded-md object-cover object-top transition-all duration-200 group-hover:scale-125 md:w-[80px] lg:h-[110px] lg:w-[110px]',
                        {
                          ' grayscale': winner_id && winner_id === agent1?.id && !!winner?.id,
                        }
                      )}
                    />

                    {winner && (
                      <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
                        <img
                          src={agent2?.id === winner?.id ? '/images/win.png' : '/images/lose.png'}
                          className={agent2?.id === winner?.id ? 'w-[44px] lg:w-[54px]' : 'w-[24px] lg:w-[34px]'}
                        />
                      </div>
                    )}
                  </div>
                  {!winner && (
                    <div className="absolute left-1/2 h-6 w-8 lg:h-[33px] lg:w-[40px] lg:translate-x-[22%]">
                      <img src="/images/vs.png" alt="logo" className="-rotate-[5deg] transition-all duration-200 group-hover:scale-150" />
                    </div>
                  )}
                </HStack>

                <HStack className="my-0.5 flex-nowrap justify-center gap-3 text-center font-jersey15 text-[#FFFFFF] text-sm uppercase sm:text-base md:text-xl lg:my-2 lg:text-2xl">
                  <span className="break-word line-clamp-1"> {agent1?.name}</span>
                  <span className="font-bold font-openSans text-[8px] lg:text-xxs">{agent1?.name && agent2?.name ? 'x' : null}</span>
                  <span className="break-word line-clamp-1">{agent2?.name}</span>
                </HStack>

                <H1 className="line-clamp-3 max-w-[420px] text-center font-bangers text-[15px] text-white leading-5 sm:text-lg md:text-2xl lg:text-[32px] lg:leading-9">
                  {topic}
                </H1>

                <div className="flex w-full justify-center">{bottomBar}</div>
              </VStack>
            </div>

            <HStack pos="center" spacing={24}>
              <Button className="hidden lg:flex" onClick={handleCopy} loading={loading}>
                Copy Image
              </Button>
              <Button onClick={handleDownload} loading={loading}>
                Save Image
              </Button>

              <Button
                onClick={() => {
                  window.open(`http://twitter.com/share?text=${topic}&url=${window.location.href}&hashtags=BOAon0G`);
                }}
              >
                Post On X
              </Button>
            </HStack>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </div>
  );
};

export default ButtonShare;
