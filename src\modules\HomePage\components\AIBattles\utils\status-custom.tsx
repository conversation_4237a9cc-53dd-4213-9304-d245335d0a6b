import { Icons } from '@/assets/icons';
import { HStack } from '@/components/utilities';

export const getStatus = (status: string, userWin?: string) => {
  switch (status) {
    case 'ongoing':
      return (
        <>
          <div className="flex h-7 w-fit items-center justify-center rounded-[9px] bg-[#FF8101] px-3 py-2 font-geistMono font-semibold text-white text-xs transition-all duration-200 group-hover:hidden">
            <p>ONGOING</p>
          </div>
          <div className="relative hidden h-7 w-fit flex-nowrap items-center justify-center gap-3 rounded-[9px] bg-[#FF8101] px-3 py-2 font-geistMono font-semibold text-white text-xs uppercase transition-all duration-200 group-hover:flex">
            View Battle <Icons.arrowRight />
            <div className="-z-[1] absolute inset-0 top-0 flex h-7 animate-blink rounded-[9px] shadow-[_0_3px_5px_2px_#ffd9ac] transition-all [transition-duration:2000ms]"></div>
          </div>
        </>
      );
    case 'upcoming':
      return (
        <HStack
          noWrap
          className="rounded-[9px] bg-[#131313] px-3 py-2 text-center font-geistMono font-semibold text-white text-xs uppercase"
        >
          Upcoming
        </HStack>
      );
    case 'completed':
      return (
        <HStack
          noWrap
          className="rounded-[9px] bg-[#131313] px-3 py-2 text-center font-geistMono font-semibold text-[#31FF08] text-xs uppercase"
        >
          {userWin} WINS!
        </HStack>
      );
    default:
      return '';
  }
};
