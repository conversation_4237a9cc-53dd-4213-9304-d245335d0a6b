'use client';

import { useBattleInfoById } from '@/api/battles/queries';
import type { IBetPlacedSocketResponse } from '@/api/battles/types';
import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import InputMask from '@/components/ui/input-mask';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show, VStack } from '@/components/utilities';
import { EEventSocket, socket } from '@/config/socket';
import { useUserLogin } from '@/hooks/useUserLogin';
import { cn, onMutateError } from '@/libs/common';
import { SMART_CONTRACT_ADDRESS } from '@/libs/const';
import { formatNumber } from '@/libs/utils';
import { ABI } from '@/libs/web3/abi';
import { customChain } from '@/libs/web3/wagmi';
import { ethers } from 'ethers';
import { useParams } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import { formatUnits } from 'viem';
import { useBalance, useWaitForTransactionReceipt, useWriteContract } from 'wagmi';

type Props = {
  agentSelected?: { label: string; value: string };
};
const presetAmounts = [1, 5, 10, 100];

const BetAmount = ({ agentSelected }: Props) => {
  const [customAmount, setCustomAmount] = useState<string>('');

  const loadingIdRef = useRef<any>(null);
  const { isLoggedIn, address } = useUserLogin();
  const { id } = useParams();
  const { data: balanceData, isFetching } = useBalance({
    address,
    chainId: customChain.id,
  });

  const { data: dataInfo } = useBattleInfoById({ variables: String(id), enabled: Boolean(id && isLoggedIn), onError: onMutateError });

  const { data: dataHash, writeContract, isLoading } = useWriteContract();
  const { isFetching: isFetchingTransaction, status } = useWaitForTransactionReceipt({
    chainId: customChain.id,
    hash: dataHash,
    query: {
      enabled: !!dataHash,
    },
  });

  const handlePlaceBet = () => {
    writeContract(
      {
        abi: ABI,
        address: SMART_CONTRACT_ADDRESS,
        functionName: 'placeBet',
        args: [BigInt(String(id)), ethers.parseUnits(customAmount, 18), BigInt(Number(agentSelected?.value))],
        value: ethers.parseUnits(customAmount, 18),
      },
      {
        onSuccess: (data) => {
          loadingIdRef.current = toast.loading('Waiting for transaction...');
        },
        onError: (error) => {
          toast.error('Please confirm the transaction to place your bet!');
        },
      }
    );
  };

  const potential_payout = useMemo(() => {
    if (!dataInfo || !agentSelected) return 0;
    const agentData: any =
      (String(dataInfo.agent1_data?.agent_id) === String(agentSelected.value) && dataInfo.agent1_data) ||
      (String(dataInfo.agent2_data?.agent_id) === String(agentSelected.value) && dataInfo.agent2_data);

    // if (!agentData) return 0;
    return (
      (Number(customAmount) * (Number(dataInfo?.total?.prize_pool) + Number(customAmount)) * (1 - Number(dataInfo?.total?.platform_fee))) /
      (Number(customAmount) + Number(agentData?.total_bet || 0))
    );
  }, [dataInfo, agentSelected, customAmount]);

  const fee = useMemo(() => {
    if (!dataInfo || !agentSelected) return 0;
    const agentData: any =
      (String(dataInfo.agent1_data?.agent_id) === String(agentSelected.value) && dataInfo.agent1_data) ||
      (String(dataInfo.agent2_data?.agent_id) === String(agentSelected.value) && dataInfo.agent2_data);

    // if (!agentData) return 0;
    return (
      (Number(customAmount) * (Number(dataInfo?.total?.prize_pool) + Number(customAmount)) * Number(dataInfo?.total?.platform_fee)) /
      (Number(customAmount) + Number(agentData?.total_bet || 0))
    );
  }, [dataInfo, agentSelected, customAmount]);

  const fee_default = useMemo(() => {
    if (!dataInfo || !agentSelected) return 0;
    const agentData: any =
      (String(dataInfo.agent1_data?.agent_id) === String(agentSelected.value) && dataInfo.agent1_data) ||
      (String(dataInfo.agent2_data?.agent_id) === String(agentSelected.value) && dataInfo.agent2_data);

    // if (!agentData) return 0;
    return (
      (Number(customAmount) * (Number(dataInfo?.total?.prize_pool) + Number(customAmount)) * 0.05) /
      (Number(customAmount) + Number(agentData?.total_bet || 0))
    );
  }, [dataInfo, agentSelected, customAmount]);

  const potential_payout_ratio = (potential_payout / Number(customAmount)) * 100 - 100;

  useEffect(() => {
    if (status === 'success' && !isFetchingTransaction && loadingIdRef.current) {
      toast.update(loadingIdRef.current, {
        render: 'Transaction confirmed. Processing your bet...',
        type: 'info',
        isLoading: true,
      });
    }

    const betPlacedListener = (data: IBetPlacedSocketResponse) => {
      if (data.userWallet?.toLocaleLowerCase() !== address?.toLocaleLowerCase()) return;

      if (loadingIdRef.current) {
        toast.update(loadingIdRef.current, {
          render: 'Your bet has been placed successfully!',
          type: 'success',
          isLoading: false,
          autoClose: 2000,
        });
        loadingIdRef.current = null;
      }
    };

    socket.once(`${EEventSocket.BET_PLACED}_${id}`, betPlacedListener);

    if (status === 'error' && !isFetchingTransaction) {
      toast.update(loadingIdRef.current, {
        render: 'Your bet placement failed. Please try again!',
        type: 'error',
        isLoading: false,
        autoClose: 2000,
      });
      loadingIdRef.current = null;
    }

    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${id}`, betPlacedListener);
    };
  }, [status, isFetchingTransaction, id, address]);

  useEffect(() => {
    if (loadingIdRef.current) {
      toast.dismiss(loadingIdRef.current);
    }
  }, []);

  return (
    <div className="">
      <div className="mb-3 font-medium text-dark-100 text-sm">BET AMOUNT</div>

      <VStack className="w-full" spacing={12}>
        <div className="flex w-full flex-no-wrap items-center gap-1">
          {presetAmounts.map((amount) => (
            <div
              key={amount}
              className={cn(
                'flex w-full cursor-pointer items-center justify-center rounded-lg border border-dark-100 bg-white px-1 py-1 text-center font-medium text-base text-dark-100 hover:bg-[#FFEB00] hover:text-tertiary-900',
                { 'border-2 border-[#FF3901] bg-[#FFEB00]': Number(customAmount) === Number(amount) }
              )}
              onClick={() => setCustomAmount(String(amount))}
            >
              {amount}
            </div>
          ))}
        </div>

        <div className="">
          {/* <div className="flex items-center text-dark-100 text-xxs uppercase">
            Current balance:{' '}
            <span className="ml-1.5 font-semibold">{balanceData ? formatNumber(Number(formatUnits(balanceData?.value, 18))) : 0}</span>{' '}
          </div> */}

          <InputMask
            decimalPlaces={5}
            // autoFocus
            placeholder="OR ENTER THE AMOUNT YOU WANT"
            value={customAmount}
            onChange={(e) => setCustomAmount(e.target.value)}
            className="w-full rounded-[8px] bg-[#FFFFFF59] px-4 py-3 text-center text-sm placeholder:text-center focus:bg-[#FFFBCC]"
          />

          {/* {balanceData && (
              <div className="-translate-y-1/2 absolute top-1/2 right-3">
                <Button
                  className="h-7 border px-3 text-xs"
                  onClick={() => setCustomAmount(formatNumber(Number(formatUnits(balanceData?.value, 18)) - 0.001))}
                >
                  Max
                </Button>
              </div>
            )} */}
        </div>
      </VStack>

      <VStack className="mt-10 w-full gap-4">
        {customAmount && Number(customAmount) > 0 && (
          <div className="flex flex-col gap-2 text-sm ">
            <HStack className="uppercase" noWrap pos="apart">
              <p className="font-medium text-dark-600 ">Fee</p>

              <HStack spacing={8}>
                <Show when={Boolean(dataInfo?.total?.platform_fee && dataInfo?.total?.platform_fee !== 0.05)}>
                  <p className="flex items-center font-medium text-dark-600 line-through ">{formatNumber(fee_default)} (5%)</p>
                </Show>

                <p
                  className={cn(
                    'flex items-center font-semibold',
                    dataInfo?.total?.platform_fee && dataInfo?.total?.platform_fee === 0.05 ? 'text-dark-600' : 'text-shamrock-700'
                  )}
                >
                  {formatNumber(fee)} ({formatNumber(Number(dataInfo?.total?.platform_fee) * 100, 2)}%)
                </p>
              </HStack>
            </HStack>

            {Number(dataInfo?.total?.platform_fee) < 0.05 && (
              <div className="-mt-1 mb-1.5 text-right font-semibold text-[#0427FA] text-sm">LIMITED TIME OFFER</div>
            )}

            <HStack noWrap pos="apart">
              <div className="flex flex-1 items-center font-medium text-dark-600">
                <span className="uppercase">Potential Payout</span>
                <Tooltip
                  className=""
                  label={
                    <>
                      Calculated based on the total prize pool and <br /> total number of bets and fees.
                    </>
                  }
                >
                  <Icons.info className="ml-1" />
                </Tooltip>
              </div>
              <p className="flex items-center font-semibold text-shamrock-700">
                {formatNumber(potential_payout)} ({formatNumber(potential_payout_ratio, 2)}%)
              </p>
            </HStack>
          </div>
        )}

        <Show when={isLoggedIn}>
          <Button
            rounded={'md'}
            loading={isLoading || isFetchingTransaction}
            onClick={handlePlaceBet}
            className="h-10 w-full font-semibold md:h-8"
            disabled={Boolean(
              !agentSelected ||
                Number(customAmount) <= 0 ||
                (balanceData && Number(customAmount) > Number(formatUnits(balanceData?.value, 18)))
            )}
          >
            {balanceData && Number(customAmount) > Number(formatUnits(balanceData?.value, 18)) ? 'Insufficient Balance' : 'Place Bet'}
          </Button>
        </Show>
      </VStack>
    </div>
  );
};

export default BetAmount;
