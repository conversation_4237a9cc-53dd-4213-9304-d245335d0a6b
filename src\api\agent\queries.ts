import { createQuery } from 'react-query-kit';
import {
  getAgentById,
  getBattleParentHistory,
  getDetailBattleAmountChart,
  getJudgeAgent,
  getJudgeHistory,
  getParentAgent,
} from './requests';
import type {
  IAgent,
  IParamsBattleChart,
  IParamsBattleHistory,
  IResponseBattleChart,
  IResponseBattleHistory,
  IResponseJudgeHistory,
} from './types';

export const useJudgeAgent = createQuery<IAgent[], void>({
  queryKey: ['/user-portal/judge-agent'],
  fetcher: () => getJudgeAgent(),
});

export const useParentAgent = createQuery<IAgent[], void>({
  queryKey: ['/user-portal/parent-agent'],
  fetcher: () => getParentAgent(),
});

export const useAgentById = createQuery<IAgent, string>({
  queryKey: ['/user-portal/judge-parent-agent'],
  fetcher: (params) => getAgentById(params),
});

export const useBattleChart = createQuery<IResponseBattleChart, IParamsBattleChart>({
  queryKey: ['/user-portal/battle-parent-chart'],
  fetcher: (params) => getDetailBattleAmountChart(params),
});

export const useBattleHistory = createQuery<IResponseBattleHistory, IParamsBattleHistory>({
  queryKey: ['/user-portal/battle-parent-history'],
  fetcher: (params) => getBattleParentHistory(params),
});

export const useJudgeHistory = createQuery<IResponseJudgeHistory, IParamsBattleHistory>({
  queryKey: ['/user-portal/battle-judge-history'],
  fetcher: (params) => getJudgeHistory(params),
});
