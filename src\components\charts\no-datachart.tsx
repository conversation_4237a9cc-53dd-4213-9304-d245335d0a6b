import React from 'react';

import { Icons } from '@/assets/icons';
import { Show, VStack } from '../utilities';

type Props = {
  isFetching: boolean;
  dataChart: any;
  content?: string;
};
const NodataChart = ({ isFetching, dataChart, content = 'No data chart available' }: Props) => {
  return (
    <Show when={isFetching || !dataChart || dataChart?.length === 0}>
      <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
        {isFetching && <Icons.loading className="animate-spin" />}

        <Show when={!isFetching && (!dataChart || dataChart.length === 0)}>
          <VStack align="center">
            <Icons.noDataTable />
            <p className="font-semibold text-gray-600 text-xs">{content}</p>
          </VStack>
        </Show>
      </div>
    </Show>
  );
};

export default NodataChart;
