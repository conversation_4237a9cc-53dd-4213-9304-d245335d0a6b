import H2 from '@/components/text/H2';
import H4 from '@/components/text/H4';
import { Button } from '@/components/ui/button';
import { Show } from '@/components/utilities';
import { useUserLogin } from '@/hooks/useUserLogin';
import { ROUTER } from '@/libs/router';
import Link from 'next/link';

const VoteOver = () => {
  const { isLoggedIn } = useUserLogin();

  return (
    <div>
      <div className="my-18">
        <H2 className="text-center font-je font-jersey15 text-3xl text-[#FF3901] [text-shadow:_1px_2px_0px_#FFEB00,_2px_4px_0px_#FFEB00] lg:text-5xl">
          GAME OVER
        </H2>

        <H4 className="mt-4 text-center font-geistMono font-semibold text-dark-100 lg:text-sm">
          The battle has ended,
          <br />
          Check out other battles
        </H4>
      </div>

      <Show when={isLoggedIn}>
        <Link href={ROUTER.BATTLES}>
          <Button className="w-full" disabled={!isLoggedIn}>
            view next battles
          </Button>
        </Link>
      </Show>
    </div>
  );
};

export default VoteOver;
