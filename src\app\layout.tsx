import { font<PERSON><PERSON>s, font<PERSON>eist, fontGeistMono, fontJersey15, fontMetropolis, fontOrbitron, fontSans, openSans } from '@/config/fonts';
import { siteConfig } from '@/config/site';
import { cn } from '@/libs/utils';
import type { Metadata, Viewport } from 'next';
import Script from 'next/script';
import 'swiper/css';
import 'swiper/css/navigation';
import './globals.css';
import Providers from './providers';

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.appUrl),
  title: siteConfig.name,
  description: siteConfig.description,
  generator: 'Next.js',
  applicationName: siteConfig.name,
  referrer: 'origin-when-cross-origin',
  keywords: [],
  authors: [{ name: siteConfig.name }],
  creator: siteConfig.name,
  publisher: siteConfig.name,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    images: [siteConfig.ogImage],
    description: siteConfig.description,
    title: {
      default: siteConfig.name,
      template: `${siteConfig.name} - %s`,
    },
  },
  icons: {
    icon: siteConfig.ogImage,
    shortcut: siteConfig.ogImage,
    apple: siteConfig.ogImage,
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: `@${siteConfig.twitterHandle}`,
  },
};

export const viewport: Viewport = {
  width: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    // { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

type RootLayoutProps = Readonly<{ children: React.ReactNode }>;

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <head>
        <link rel="preload" as="image" href="/images/bg_passcode.png" />
        <link rel="preload" as="image" href="/images/background.png" />

        <Script src="https://www.googletagmanager.com/gtag/js?id=G-Q4RT6W8PM1" strategy="afterInteractive" />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-Q4RT6W8PM1');
          `}
        </Script>
        {/* hotjar */}
        <Script id="hotjar" strategy="afterInteractive">
          {`
            (function(h,o,t,j,a,r){
              h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
              h._hjSettings={hjid:6482676,hjsv:6};
              a=o.getElementsByTagName('head')[0];
              r=o.createElement('script');r.async=1;
              r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
              a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
          `}
        </Script>
      </head>
      <body
        cz-shortcut-listen="true"
        className={cn(
          'min-h-screen bg-background font-geistMono antialiased',
          openSans.variable,
          fontSans.variable,
          fontOrbitron.variable,
          fontMetropolis.variable,
          fontGeistMono.variable,
          fontBangers.variable,
          fontJersey15.variable,
          fontGeist.variable
        )}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
