import type { IPagination } from '@/types';
import type { IAgent } from '../agent/types';

export interface Bet {
  user_username: string;
  agent_name: string;
  user_avatar: string;
  total_bet_amount: number;
  rank: number;
  user_id: string;
}

export interface IResponsiveTopBattors {
  current_user_data: Bet;
  data: Bet[];
}

export interface IParamsBattleActivity {
  agent_id?: string;
  battle_id?: string;
  limit?: number;
  page?: number;
}

export interface IResponsiveBattleActivity {
  pagination: IPagination;
  data: IDataBattleActivity[];
}

export interface IDataBattleActivity {
  user__username: string;
  agent__name: string;
  user__avatar: string;
  created_at: string;
  total_bet_amount: number;
}

export interface IBattleDetail {
  id: string;
  topic: string;
  slug: string;
  content: string;
  agent1: IAgent;
  agent2: IAgent;
  start_at: string;
  duration: number;
  status: string;
  winner: IAgent;
  winner_id: string;
  is_resolved: boolean;
  banner_url: string;
  banner_debate_end_url: string;
  resolved_onchain_status: string;
  mark_as_refunded_onchain_status: string;
  refunded_onchain_status: string;
  is_manual_admin_refund: boolean;
}

export interface IAgentReport {
  agent_id: string;
  total_bet: number;
  image_url: string;
  count_bet: number;
  name: string;
  fee: number;
  current_bet: number;
  potential_payout: number;
  potential_payout_ratio: number;
}
export interface IBattleReport {
  agent1_data: IAgentReport;
  agent2_data: IAgentReport;
  total: {
    platform_fee: number;
    prize_pool: number;
    count_bet: number;
  };
}

export interface IParamsBattle {
  limit?: number;
  page?: number;
  end_date?: string;
  start_date?: string;
  sort_key?: string;
  sort_type?: string;
  status?: string;
}

export interface IResponseBattleDashboard {
  pagination: IPagination;
  data: IDataBattleDashboard[];
}

export interface IDataBattleDashboard {
  id: number;
  topic: string;
  slug: string;
  status: string;
  start_at: string;
  agent1: {
    id: number;
    name: string;
    age: number;
    social_url: string;
    image_url: string;
    background: string;
  };
  agent2: {
    id: number;
    name: string;
    age: number;
    social_url: string;
    image_url: string;
    background: string;
  };
  public_at: string;
  duration: number;
  content: string;
  platform_fee: number;
  winner_id: number | null;
  winner: any;
  created_at: string;
  updated_at: string;
}

export interface ILiveBattleQuery {
  battle_id?: string;
  page: number;
  limit: number;
  sort_key?: string;
  sort_type?: string;
}

export interface ILiveBattleResponse {
  pagination: IPagination;
  data: IDataLiveBattle[];
}

export interface IDataLiveBattle {
  id: number;
  content: string;
  agent_id: number;
  battle_id: number;
  created_at: string;
  updated_at: string;
  agent: {
    id: number;
    name: string;
    image_url: string;
    social_url: string;
  };
  is_end: boolean;
}
export interface ICurrentUserClaim {
  id: string;
  battle_id: string;
  amount: number;
  user_wallet: string;
}

export interface IUserBettingResponse {
  pagination: IPagination;
  data: {
    id: string;
    username: string;
    avatar: string;
    wallet_address: string;
  }[];
}

export interface IJudgeComment {
  id: string;
  battle_id: string;
  agent_id: string;
  comment: string;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  social_url: string;
  image_url: string;
  voted_agent_id: string;
}

export interface IYourBet {
  id: string;
  name: string;
  agent_id: string;
  image_url: string;
  bet_amount: number;
  wallet_address: string;
  potential_payout: number;
  potential_payout_ratio: number;
}

export interface IBetPlacedSocketResponse {
  agentId: string;
  battleId: string;
  betAmount: number;
  userWallet: string;
  txnHash: string;
}
