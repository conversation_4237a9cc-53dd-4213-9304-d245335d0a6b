import { Slot } from '@radix-ui/react-slot';
import { type VariantProps, cva } from 'class-variance-authority';
import * as React from 'react';

import { Icons } from '@/assets/icons';
import { cn } from '@/libs/utils';

const buttonVariants = cva(
  'inline-flex items-center font-semibold active:scale-90 justify-center rounded-sm text-sm ring-offset-background transition-transform focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'uppercase font-geist-mono bg-[#FFF] shadow-[-1px_-2px_2px_1px_rgba(0,0,0,0.25)_inset,0px_0px_0px_3px_#000] text-[#131313] rounded-[7px] hover:bg-[#FFEB00] hover:text-[#FF3901] hover:shadow-[-1px_-2px_4px_1px_rgba(0,0,0,0.24)_inset,_0_0_0_3px_#FF3901]',
        primary: 'uppercase bg-primary-700 text-white hover:bg-primary-800',
        terq: 'bg-secondary-500 border border-[#5832201A] text-white font-semibold hover:bg-secondary-600',
        white: 'text-tertiary-900 bg-white hover:text-white hover:bg-red-damask-500',
        damask: 'text-white bg-red-damask-500 hover:bg-red-damask-600',
        outline: 'border border-[#131313] uppercase text-[#131313] rounded-[7px]',
        alert: 'bg-amaranth-600 text-white hover:bg-amaranth-700',
        gray: 'bg-[#5832201A] hover:bg-[#58322080] text-tertiary-900 border text-tertiary-900 border-[#58322026]',
        transparent: 'border-none outline-none',
      },
      size: {
        default: 'h-8 sm:h-10 px-3 sm:py-1.5',
        md: 'h-9 px-4 py-2 text-base',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-12 rounded-md sm:px-6 px-4',
        icon: 'h-9 w-9',
      },
      rounded: {
        default: 'rounded-lg',
        sm: 'rounded-sm',
        full: 'rounded-full',
        md: 'rounded-md',
        none: 'rounded-none',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, rounded, children, loading, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className, rounded }), {
          'pointer-events-none opacity-80': loading,
        })}
        ref={ref}
        {...props}
      >
        {asChild ? (
          children
        ) : (
          <>
            {children}
            {loading && <Icons.loading className="ml-4 animate-spin" />}
          </>
        )}
      </Comp>
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
