import Footer from '@/layouts/MainLayout/Footer';
import BannerAgentsDetail from '@/modules/AgentsDetailPage/components/BannerAgentsDetail';

type LayoutProps = Readonly<{ children: React.ReactNode }>;

const Layout = ({ children }: LayoutProps) => {
  return (
    <>
      <BannerAgentsDetail />
      <main className="mx-auto w-full max-w-[1440px] flex-1">{children}</main>
      <Footer />
    </>
  );
};

export default Layout;
