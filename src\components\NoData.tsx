import { Icons } from '@/assets/icons';
import H1 from '@/components/text/H1';
import { Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import type { ReactNode } from 'react';

type Props = {
  Icon: ReactNode;
  Title: ReactNode;
  Paragraph: ReactNode;
  Button?: ReactNode;

  isFetching?: boolean;
  data?: any;
  content?: string;
  className?: string;
  classNames?: {
    title?: string;
    paragraph?: string;
  };
};

const NoData = (props: Props) => {
  const { Icon, Title, Paragraph, Button, isFetching, data, content, className, classNames } = props;
  return (
    <Show when={isFetching || !data || data?.length === 0}>
      <div className={cn('m-auto flex w-full items-center justify-center', className)}>
        {isFetching && <Icons.loading className="animate-spin" />}

        <Show when={!isFetching && (!data || data.length === 0)}>
          <VStack align="center" className="w-full">
            <VStack className="m-w-[330px] items-center gap-6 text-center">
              {Icon}
              <H1
                className={cn('font-jersey15 font-normal text-[#FF3901] uppercase leading-[normal]', classNames?.title)}
                style={{
                  textShadow: '1px 2px 0px #ffeb00, 2px 4px 0px #ffeb00',
                }}
              >
                {Title}
              </H1>
              <p
                className={cn(
                  'text-center font-geistMono font-medium text-[#131313] text-sm uppercase not-italic leading-[150%]',
                  classNames?.paragraph
                )}
              >
                {Paragraph}
              </p>

              {Button}
            </VStack>
          </VStack>
        </Show>
      </div>
    </Show>
  );
};

export default NoData;
