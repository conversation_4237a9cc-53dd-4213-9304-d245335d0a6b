'use client';
import { useBattleById } from '@/api/battles/queries';
import { Button } from '@/components/ui/button';
import { Show, VStack } from '@/components/utilities';
import ConnectWallet from '@/components/wallet/ConnectWallet';
import useCheckTimeOverBattle from '@/hooks/useCheckTimeOverBattle';
import { useUserLogin } from '@/hooks/useUserLogin';
import { onMutateError } from '@/libs/common';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import BetAmount from './BetAmount';
import BetOn from './BetOn';
import BettingClosesIn from './BettingClosesIn';
import VoteInProgress from './VoteInProgress';
import VoteOver from './VoteOver';
import VoteRefund from './VoteRefund';

const FlowChannel = () => {
  const [agentSelected, setAgentSelected] = useState<{ label: string; value: string } | undefined>(undefined);

  const { id } = useParams();
  const { isLoggedIn } = useUserLogin();

  const { data } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });
  const { isBattleStillActive, isBattleStillBet } = useCheckTimeOverBattle({
    duration: data?.duration,
    start_at: data?.start_at,
  });

  return (
    <div>
      <BettingClosesIn />

      <Show when={data?.status !== 'cancelled'}>
        <Show when={isBattleStillBet && !data?.is_resolved}>
          <VStack spacing={24}>
            <BetOn agentSelected={agentSelected} setAgentSelected={setAgentSelected} />
            {isBattleStillBet && <BetAmount agentSelected={agentSelected} />}
          </VStack>
        </Show>

        <Show when={Boolean(!isBattleStillActive && data?.is_resolved)}>
          <VoteOver />
        </Show>

        <Show when={Boolean(!!data && !isBattleStillBet && !data?.is_resolved)}>
          <VoteInProgress />
        </Show>
      </Show>

      <Show when={data?.status === 'cancelled'}>
        <VoteRefund {...data} />
      </Show>

      {!isLoggedIn && (
        <ConnectWallet>
          <Button className="mt-5 h-10 w-full px-3 py-1.5 font-geistMono text-black uppercase md:h-8">Connect wallet</Button>
        </ConnectWallet>
      )}
    </div>
  );
};

export default FlowChannel;
