import { Icons } from '@/assets/icons';
import { HStack, VStack } from '@/components/utilities';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';

const CountdownTimer = ({
  targetDate,
  refetch,
  setIsStatus,
}: { targetDate: string; refetch?: () => void; setIsStatus: React.Dispatch<React.SetStateAction<string | undefined>> }) => {
  const calculateTimeLeft = () => {
    if (!targetDate || isNaN(new Date(targetDate).getTime())) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const now = new Date().getTime();
    const target = new Date(targetDate).getTime();
    const difference = target - now;

    if (difference <= 0) {
      if (refetch) {
        refetch();
        setIsStatus('ongoing');
      }
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
    const minutes = Math.floor((difference / 1000 / 60) % 60);
    const seconds = Math.floor((difference / 1000) % 60);

    return { days, hours, minutes, seconds };
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    if (!targetDate || isNaN(new Date(targetDate).getTime())) return;

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [targetDate, refetch]);

  const formatNumber = (num: number) => String(num).padStart(2, '0');

  return (
    <VStack spacing={48} className="mx-auto items-center justify-center py-7 text-center">
      <div className="flex size-[72px] items-center justify-center rounded-[14px] bg-[#FFEB00]">
        <Icons.battle />
      </div>
      <VStack spacing={24}>
        <p className="font-geistMono font-medium text-base text-dark-600 uppercase">BATTLE STARTS IN</p>

        <HStack noWrap className="items-start gap-0 font-geistMono font-semibold text-primary-700 md:gap-4">
          {timeLeft.days > 0 ? (
            <>
              <TimeBlock value={formatNumber(timeLeft.days)} label="DAYS" delay={0} />
              <Colon />
              <TimeBlock value={formatNumber(timeLeft.hours)} label="HRS" delay={800} />
              <Colon />
              <TimeBlock value={formatNumber(timeLeft.minutes)} label="MINS" delay={600} />
              <Colon />
              <TimeBlock value={formatNumber(timeLeft.seconds)} label="SECS" delay={100} />
            </>
          ) : (
            <>
              <TimeBlock value={formatNumber(timeLeft.hours)} label="HRS" delay={800} />
              <Colon />
              <TimeBlock value={formatNumber(timeLeft.minutes)} label="MINS" delay={600} />
              <Colon />
              <TimeBlock value={formatNumber(timeLeft.seconds)} label="SECS" delay={100} />
            </>
          )}
        </HStack>

        <div className="font-geistMono font-medium text-base text-dark-600 uppercase">
          {/* Start time:  */}
          {moment(targetDate).format('MM/DD/YYYY - hh:mm A')}
        </div>
      </VStack>
    </VStack>
  );
};
export const TimeBlock = ({ value, label, delay }: { value: string; label: string; delay?: number }) => {
  return (
    <VStack spacing={12}>
      <HStack className="gap-0 md:gap-1">
        <AnimatedDigitColumn digit={parseInt(value[0])} delay={Number(delay) * 4} />
        <AnimatedDigitColumn digit={parseInt(value[1])} delay={delay} />
      </HStack>
      <p className="font-geistMono font-medium text-base text-dark-600 uppercase">{label}</p>
    </VStack>
  );
};

export default CountdownTimer;

export const Colon = () => (
  <span className="h-[72px] px-2 font-bold font-geistMono text-[32px] text-dark-100 leading-[72px] md:text-[52px]">:</span>
);

const AnimatedDigitColumn = ({ digit, delay = 0 }: { digit: number; delay?: number }) => {
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const height = 72;
    if (wrapperRef.current) {
      wrapperRef.current.style.transitionDuration = `${digit * delay}ms`;
      wrapperRef.current.style.transform = `translateY(-${digit * height}px)`;
    }
  }, [digit]);

  const digits = Array.from({ length: 10 }, (_, i) => i);

  return (
    <div className="w- h-[72px] overflow-hidden rounded md:w-[35px]">
      <div ref={wrapperRef} className="transition-all duration-300 ease-in-out">
        {digits.map((d) => (
          <div
            style={{ height: '72px' }}
            key={d}
            className="flex h-[72px] items-center justify-center font-bold font-geistMono text-[32px] text-dark-100 uppercase leading-none md:text-[62px]"
          >
            {d}
          </div>
        ))}
      </div>
    </div>
  );
};
