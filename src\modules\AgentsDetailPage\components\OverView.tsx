import type { IAgent } from '@/api/agent/types';
interface Props {
  data: IAgent | undefined;
  isFetching: boolean;
}
const OverView = ({ data, isFetching }: Props) => {
  return <></>;
  // return (
  //   <VStack spacing={20}>
  //     <VStack spacing={6}>
  //       <SkeletonWrapper loading={isFetching}>
  //         <p className="font-semibold text-base text-tertiary-900">Description</p>
  //       </SkeletonWrapper>
  //       <SkeletonWrapper loading={isFetching}>
  //         <p className="font-normal text-[#341B14] text-base">{data?.description ?? '-'}</p>
  //       </SkeletonWrapper>
  //     </VStack>
  //     <Show when={data && data?.capabilities?.length > 0}>
  //       <VStack spacing={6}>
  //         <SkeletonWrapper loading={isFetching}>
  //           <p className="font-semibold text-base text-tertiary-900">Capability</p>
  //         </SkeletonWrapper>
  //         <HStack spacing={8}>
  //           {data?.capabilities?.map((cap, index) => (
  //             <SkeletonWrapper loading={isFetching} key={index}>
  //               <div className="w-fit rounded-2xl border-[#2B211F] border-[1.5px] px-3 py-1 font-medium text-[#2B211F] text-sm">{cap}</div>
  //             </SkeletonWrapper>
  //           ))}
  //         </HStack>
  //       </VStack>
  //     </Show>
  //     {data?.metrics?.map((item, index) => (
  //       <VStack spacing={6} key={index}>
  //         <SkeletonWrapper loading={isFetching}>
  //           <p className="font-semibold text-base text-tertiary-900">{item?.metric}</p>
  //         </SkeletonWrapper>
  //         <HStack spacing={8} className="text-[#341B14]" noWrap align={'start'}>
  //           <div className="mt-[0.3rem]">
  //             <Icons.maximize />
  //           </div>
  //           <span>{item?.properties}</span>
  //         </HStack>
  //       </VStack>
  //     ))}
  //   </VStack>
  // );
};

export default OverView;
