'use client';
import Container from '@/components/wrapper/Container';
import AIBattles from './components/AIBattles';
import DebateAgents from './components/DebateAgents';
import DebateGoLive from './components/DebateGoLive';
import LeaderBoard from './components/LeaderBoard';

const HomePage = () => {
  // const { data: dataHash, writeContract, isLoading } = useWriteContract();
  // const { isFetching: isFetchingTransaction, status } = useWaitForTransactionReceipt({
  //   chainId: customChain.id,
  //   hash: dataHash,
  //   query: {
  //     enabled: !!dataHash,
  //   },
  // });

  // const { data } = useReadContract({
  //   abi: ABI,
  //   address: SMART_CONTRACT_ADDRESS,
  //   functionName: 'getDebateInfo',
  //   args: [BigInt(59)],
  // });

  return (
    <Container className="p-0 lg:p-0">
      {/* <button
        onClick={() =>
          writeContract(
            {
              abi: ABI,
              address: SMART_CONTRACT_ADDRESS,
              functionName: 'adminResolveDebate',
              args: [BigInt(60), BigInt(Number(23))],
            },
            {
              onSuccess: (data) => {},
              onError: (error) => {
                console.log(error);
                toast.error('Please confirm the transaction to place your bet!');
              },
            }
          )
        }
      >
        click
      </button> */}
      <AIBattles />

      <DebateGoLive />

      <DebateAgents />

      <LeaderBoard />
    </Container>
  );
};

export default HomePage;
