'use client';

import { useState } from 'react';
import { type Connector, useAccount, useConnect, useDisconnect, useSignMessage, useSwitchChain } from 'wagmi';

import { connectWallet } from '@/api/auth/requests';
import { Icons } from '@/assets/icons';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { env } from '@/libs/const';
import { WALLET_SUPPORTS } from '@/libs/web3/web3.const';
import { useUserStore } from '@/stores/UserStore';
import type { FCC } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { setCookie } from 'cookies-next';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import H2 from '../text/H2';
import { Button } from '../ui/button';
import { HStack, VStack } from '../utilities';

interface Props {}

const ConnectWallet: FCC<Props> = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [walletName, setWalletName] = useState<string | undefined>();

  const { disconnect, disconnectAsync } = useDisconnect();
  const { isConnected } = useAccount();
  const { mutate, isLoading: isConnectWalletLoading } = useMutation(connectWallet);
  const { setUser, setIsVerifiedUser, setVerifyToken } = useUserStore();
  const { connectAsync, connectors, isLoading: loadingConnect } = useConnect();
  const { signMessageAsync, isLoading: isLoadingSign } = useSignMessage();
  const { chains, switchChain } = useSwitchChain();

  const router = useRouter();

  const isLoadingConnect = Boolean(isConnectWalletLoading || loadingConnect || isLoadingSign);

  const handleConnect = async (walletName: string) => {
    const connector = connectors.find((connector) => connector.name === walletName) as Connector;
    setWalletName(walletName);

    if (walletName === 'MetaMask' || !connector) {
      if (!(typeof (window as any).ethereum !== 'undefined')) {
        window.open('https://metamask.io/', '_blank');
        return;
      }
    }

    try {
      if (isConnected) {
        await disconnectAsync();
      }

      if (!env.SIGN_MESSAGE_WALLET) {
        toast.error('Missing signature message!');
        return;
      }

      const timestamp = new Date().getTime();
      const { accounts, chainId } = await connectAsync({ connector });

      if (chainId !== chains[0].id) {
        try {
          await switchChain({ chainId: chains[0].id });
          await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (switchError: any) {
          if (switchError.code === 4902) {
            toast.error('Network not added to your wallet. Please add it manually.');
          } else {
            toast.error('Failed to switch network!');
          }
          disconnect();
          return;
        }
      }

      const signature = await signMessageAsync({ message: env.SIGN_MESSAGE_WALLET + timestamp });

      if (!signature) {
        toast.error('Get signature fail!');
        return;
      }

      if (accounts && accounts?.[0] && signature) {
        mutate(
          {
            wallet_address: accounts[0]?.toLowerCase(),
            signature,
            nonce: String(timestamp),
          },
          {
            onSuccess: ({ access_token, access_token_ttl, verify_token, is_verified_user, refresh_token, refresh_token_ttl, user }) => {
              setIsVerifiedUser(is_verified_user);
              if (verify_token) {
                setVerifyToken(verify_token);
              }
              setOpen(false);
              if (!is_verified_user) return;

              setCookie('access_token', access_token, { maxAge: access_token_ttl * 60 });
              setCookie('refresh_token', refresh_token, {
                maxAge: refresh_token_ttl * 60,
              });
              setUser(user);
              toast.success('Connect wallet successfully');
              router.refresh();
            },
            onError: (error) => {
              disconnect();
              toast.error('Please check your wallet — it might be locked, a request may be pending, or was rejected.');
            },
          }
        );
      }
    } catch (err: any) {
      disconnect();
      console.log('erro', err);
      if (err?.code === 4001) {
        toast.error('User rejected the request.');
        return;
      }
      // toast.error(err?.message ?? 'Unexpected problem!');
      toast.error('Please check your wallet — it might be locked, a request may be pending, or was rejected.');
    }
  };

  return (
    <Dialog open={open} modal>
      <DialogTrigger asChild onClick={() => setOpen(true)}>
        {children}
      </DialogTrigger>
      <DialogContent
        isRemoveScroll={Boolean(!walletName || walletName === 'MetaMask')}
        className="z-[60] w-full max-w-[340px] gap-1 rounded-[24px] px-6 sm:px-8 md:max-w-[498px]"
      >
        <HStack pos="right">
          <button className="hover:opacity-75" onClick={() => setOpen(false)}>
            <Icons.X />
          </button>
        </HStack>
        <div className="lg:px-14">
          <H2 className="pt-2 text-center font-bangers font-medium text-4xl text-[#000000] md:pt-8 lg:text-5xl">Connect wallet</H2>

          <VStack spacing={20} className="mt-12 mb-4">
            {WALLET_SUPPORTS.map((wallet) => {
              return (
                <Button
                  key={wallet.name}
                  onClick={() => handleConnect(wallet.name)}
                  loading={walletName === wallet.name && isLoadingConnect}
                  size="lg"
                  disabled={walletName !== wallet.name && isLoadingConnect}
                >
                  <HStack className="w-full justify-center">
                    <Image src={wallet.src} width={24} height={24} alt={wallet.name} />
                    <span className="font-bold text-[#000000] text-base sm:ml-4 sm:text-sm">{wallet.title}</span>

                    {/* {!isLoadingConnect && <Icons.arrowRight className="stroke-tertiary-900 hover:stroke-white" />} */}
                  </HStack>
                </Button>
              );
            })}
          </VStack>

          <div className="mt-8 pb-4 text-center font-medium text-dark-600 lg:pb-8">MORE WALLETS COMING SOON...</div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConnectWallet;
