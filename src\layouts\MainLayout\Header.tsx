'use client';
import Tabs from '@/components/TabsV2';
import { Button } from '@/components/ui/button';
import { Show } from '@/components/utilities';
import ConnectWallet from '@/components/wallet/ConnectWallet';
import useMobile from '@/hooks/useMobile';
import { useUserLogin } from '@/hooks/useUserLogin';
import { env } from '@/libs/const';
import { ROUTER } from '@/libs/router';
import { cn } from '@/libs/utils';
import { useUserStore } from '@/stores/UserStore';
import { ListTableBattleDetail, useMobileTabBattleStore } from '@/stores/useMobileTabBattleStore';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAccount } from 'wagmi';
import CreateAccountDialog from './CreateAccountDialog';
import Sidebar from './Sidebar';
import SwitchChain from './SwitchChain';
import UserInfo from './UserInfo';
import { MENU_NAVS } from './libs/consts';

type Props = {
  className?: string;
};

const REGEX_PATH_BATTLE_DETAIL = /^\/battles\/([^/]+)$/;

const Header = (props: Props) => {
  const { className } = props;

  const [isOpenCreateAccount, setIsOpenCreateAccount] = useState<boolean>(false);
  const isMobile = useMobile();
  const { isLoggedIn } = useUserLogin();
  const { isVerifiedUser } = useUserStore();
  const pathname = usePathname();
  const { chainId } = useAccount();

  const { tab, setTab } = useMobileTabBattleStore();

  useEffect(() => {
    if (isVerifiedUser === false) {
      setIsOpenCreateAccount(true);
    }
  }, [isVerifiedUser]);

  const [openSwitchChainDialog, setOpenSwitchChainDialog] = useState(false);

  useEffect(() => {
    if (chainId && chainId !== env.CHAIN_ID) {
      setOpenSwitchChainDialog(true);
    } else {
      setOpenSwitchChainDialog(false);
    }
  }, [chainId]);

  return (
    <>
      <header className={cn('z-50 border-[#E7E7E7] border-b', className)}>
        <section className="mx-auto flex w-full max-w-[1440px] items-center justify-between p-6 sm:px-4 sm:py-8 lg:px-8">
          <Link href={ROUTER.HOME} scroll={false}>
            <div className="relative h-[56px] w-[108px] sm:h-[80px] sm:w-[142px]">
              <Image src="/images/logo-header.png" alt="logo" fill sizes="100vh" priority />
            </div>
          </Link>
          <Show when={isMobile}>
            <Sidebar />
          </Show>

          <Show when={!isMobile}>
            <nav className="flex items-center font-semibold text-base text-black uppercase leading-[150%]">
              {MENU_NAVS.map((nav) => {
                return (
                  <Link
                    scroll={false}
                    href={nav.link}
                    key={nav.title}
                    className={cn(
                      'rounded-[7px] px-6 uppercase hover:bg-[#FFEB00] hover:py-1.5 hover:text-[#FF3901] hover:shadow-[_0_0_0_2px_#FF3901]',
                      {
                        // 'text-[#000000]/1000': pathname === nav.link,
                        'text-[#FFF]': pathname === '/',
                      }
                    )}
                  >
                    {nav.title}
                  </Link>
                );
              })}
            </nav>
          </Show>
          <CreateAccountDialog open={isOpenCreateAccount} setOpen={setIsOpenCreateAccount} />
          <Show when={isLoggedIn && !isMobile}>
            <UserInfo />
          </Show>
          <Show when={!isLoggedIn && !isMobile}>
            <ConnectWallet>
              <Button className="px-3 py-1.5 font-geistMono font-semibold text-black uppercase">Connect wallet</Button>
            </ConnectWallet>
          </Show>
        </section>
        <Show when={isMobile && !!pathname.match(REGEX_PATH_BATTLE_DETAIL)}>
          <Tabs
            tabs={tab}
            setTabs={(value) => setTab(value as any)}
            data={ListTableBattleDetail}
            className="scrollbar-hidden mx-2 mb-2 overflow-auto rounded-none border-none p-0"
            itemClassName="whitespace-nowrap overflow-visible !rounded-[6px]"
          />
        </Show>
      </header>
      <Show when={openSwitchChainDialog}>
        <SwitchChain openSwitchChainDialog={openSwitchChainDialog} setOpenSwitchChainDialog={setOpenSwitchChainDialog} />
      </Show>
    </>
  );
};

export default Header;
