import type { IAgent } from '@/api/agent/types';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, Show, VStack } from '@/components/utilities';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import Link from 'next/link';

type Props = {
  loading?: boolean;
} & Partial<IAgent>;

const CardDebaterAgents = ({ id, loading, name, image_url, description, debate_count, win_count, rate, agent_type }: Props) => {
  return (
    <VStack className="gap-6 rounded-xl">
      <SkeletonWrapper loading={loading}>
        <Link href={`/agents/${String(id)}?type=${agent_type}`}>
          <div className="relative aspect-square w-full overflow-hidden rounded-[14px]">
            <Image src={image_url || '/images/no-image.png'} alt="logo" fill sizes="(max-width: 768px) 100vw" />
          </div>
        </Link>
      </SkeletonWrapper>

      <VStack className="gap-1.5">
        <SkeletonWrapper loading={loading} className="w-20">
          <span className="font-geistMono text-[#A1A1A1] text-xs uppercase">Debate agent</span>
        </SkeletonWrapper>
        <SkeletonWrapper loading={loading} className="w-20">
          <span className="font-jersey15 font-normal text-[#FFF] text-[2rem] uppercase leading-[normal]">{name}</span>
        </SkeletonWrapper>

        <div className="my-2 flex-1">
          {/* <SkeletonWrapper loading={loading} className="h-10 w-full">
            <p className="line-clamp-5 font-normal text-[#2B211F] text-sm">{description}</p>
          </SkeletonWrapper> */}

          <SkeletonWrapper loading={loading}>
            <Show when={Number(debate_count) >= 0}>
              <HStack className="gap-0 text-sm" pos="apart" noWrap>
                <VStack className="gap-0">
                  <span className="font-medium text-[#A1A1A1] uppercase">Battles</span>
                  <span className="font-geistMono font-semibold text-[#FFF]">{debate_count}</span>
                </VStack>

                <VStack className="gap-0">
                  <span className="font-medium text-[#A1A1A1] uppercase">Wins</span>
                  <span className="font-geistMono font-semibold text-[#31FF08]">
                    {win_count} ({formatNumber(rate, 2)}%)
                  </span>
                </VStack>
              </HStack>
            </Show>
          </SkeletonWrapper>
        </div>

        {/* <SkeletonWrapper loading={loading}>
          <Button size="sm" className="bg-secondary-500 font-semibold">
            <HStack spacing={6}>
              <span>View Details</span>

              <Icons.arrowRight />
            </HStack>
          </Button>
        </SkeletonWrapper> */}
      </VStack>
    </VStack>
  );
};

export default CardDebaterAgents;
