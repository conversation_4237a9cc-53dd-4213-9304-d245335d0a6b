'use client';

import { useParentAgent } from '@/api/agent/queries';
import { HStack, Show, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import DebaterAgentItem from './DebaterAgentItem';

const DebaterAgent = () => {
  const { data, isFetching } = useParentAgent({ onError: onMutateError });

  return (
    <VStack spacing={20}>
      <HStack className="justify-between">
        <div>
          <p className="align-middle font-bangers font-normal text-[32px] leading-[140%] tracking-[0%] sm:text-5xl">Debaters</p>
          <p className="mt-3 align-middle font-geistMono font-medium text-[#717171] text-base uppercase leading-[150%] tracking-[0%]">
            DEBATERS are built to battle it out — armed with logic, wit, and bold personalities. <br /> They each bring unique strengths,
            strategies, and a DEBATE style all their own.
          </p>
        </div>
      </HStack>

      <div className="mt-6 grid grid-cols-1 gap-8 sm:grid-cols-3 lg:grid-cols-5">
        <Show when={isFetching}>
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
        </Show>

        <Show when={!isFetching}>
          {data?.map((item) => (
            <DebaterAgentItem key={item.id} {...item} />
          ))}
        </Show>
      </div>
    </VStack>
  );
};

export default DebaterAgent;
