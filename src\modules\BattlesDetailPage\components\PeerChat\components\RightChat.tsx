import { useListChildComments } from '@/api/comment/queries';
import { reactionComment, replyComment } from '@/api/comment/requests';
import type { IComment, ICommentResponse } from '@/api/comment/types';
import { Icons } from '@/assets/icons';
import NoData from '@/components/NoData';
import { Separator } from '@/components/ui/separator';
import { HStack, Show, VStack } from '@/components/utilities';
import { useUserLogin } from '@/hooks/useUserLogin';
import { cn, onMutateError } from '@/libs/common';
import { type InfiniteData, useMutation } from '@tanstack/react-query';
import { ChevronDown, MessageCircle, Send } from 'lucide-react';
import moment from 'moment';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
interface IRightChatProps {
  listComment: InfiniteData<ICommentResponse> | undefined;
  refetch: () => void;
  ref: (node?: Element | null) => void;
  isFetching: boolean;
  expandedParentId: string | null;
  setExpandedParentId: React.Dispatch<React.SetStateAction<string | null>>;
}
const RightChat = ({ listComment, refetch, ref, isFetching, expandedParentId, setExpandedParentId }: IRightChatProps) => {
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState<string>('');
  const { isLoggedIn } = useUserLogin();
  const params = useParams();
  const id = params?.id;

  const { data: listChildComment, refetch: refetchChild } = useListChildComments({
    variables: { parent_id: expandedParentId },
    enabled: !!expandedParentId,
    refetchOnMount: true,
  });

  const { mutate: mutateReaction } = useMutation(reactionComment, {
    onSuccess: () => {
      refetch();
      refetchChild();
    },
    onError: onMutateError,
  });

  const { mutate: mutateReply, isLoading: isReplying } = useMutation(replyComment, {
    onSuccess: () => {
      setReplyingTo(null);
      setReplyText('');
      refetch();
      refetchChild();
    },
    onError: onMutateError,
  });

  const toggleShowChildComments = (parentId: string) => {
    if (expandedParentId === parentId) {
      setExpandedParentId(null);
    } else {
      setExpandedParentId(parentId);
    }
  };

  const handleReplyClick = (parentId: string, username?: string) => {
    setReplyingTo(replyingTo === parentId ? null : parentId);
    setReplyText(username ? `@${username} ` : '');
  };

  const handleSubmitReply = (parentId: string) => {
    if (!replyText.trim()) return;
    mutateReply(
      { comment: replyText, battle_id: String(id), parent_id: parentId },
      {
        onSuccess: () => {
          setExpandedParentId(parentId);
        },
      }
    );
  };

  return (
    <div>
      <VStack spacing={16} className="h-full w-full font-geistMono md:px-4 lg:px-6">
        {listComment?.pages.map((group, i) => (
          <React.Fragment key={i}>
            {group.data.map((item) => (
              <VStack key={item.id} className="w-full rounded-2xl bg-white p-4 text-black">
                <VStack spacing={12}>
                  <HStack pos={'apart'} noWrap>
                    <HStack spacing={12} noWrap>
                      <div className="h-7 w-7">
                        <Image src={item?.user?.avatar ?? ''} alt="" width={32} height={32} className="h-7 w-7 rounded-md" />
                      </div>
                      <VStack className="font-geistMono font-medium text-xs uppercase" spacing={4}>
                        {item?.user?.username}
                        <span className="whitespace-nowrap">{moment(item?.created_at).format('hh:mm A DD/MM/YYYY')}</span>
                      </VStack>
                    </HStack>
                    <HStack pos={'apart'} noWrap>
                      <HStack spacing={4} noWrap>
                        <span
                          className={cn(
                            'font-geistMono font-medium text-black text-xs',
                            item?.user_reaction_type === 'LOVE' && 'text-red-600'
                          )}
                        >
                          {item?.reaction_count}
                        </span>
                        <Icons.heart
                          size={16}
                          className="cursor-pointer"
                          color={item?.user_reaction_type === 'LOVE' ? '#FF0000' : '#000000'}
                          onClick={() => {
                            if (isLoggedIn) {
                              mutateReaction({ comment_id: item?.id, reaction: item?.user_reaction_type === 'LOVE' ? '' : 'LOVE' });
                            }
                          }}
                        />
                      </HStack>
                    </HStack>
                  </HStack>

                  <VStack spacing={12}>
                    <p className="font-geistMono font-normal text-sm">{item?.comment}</p>
                    <HStack pos={item?.child_count > 0 ? 'apart' : 'right'}>
                      <Show when={item?.child_count > 0}>
                        <HStack spacing={4} onClick={() => toggleShowChildComments(item?.id)}>
                          <span className="cursor-pointer font-medium text-xs uppercase">
                            {expandedParentId === item.id
                              ? `Hide ${item?.child_count} ${item?.child_count === 1 ? 'reply' : 'replies'}`
                              : `Show ${item?.child_count} ${item?.child_count === 1 ? 'reply' : 'replies'}`}
                          </span>
                          <ChevronDown size={16} />
                        </HStack>
                      </Show>
                      <Show when={isLoggedIn}>
                        <HStack spacing={4} className="cursor-pointer" onClick={() => handleReplyClick(item.id)}>
                          <span className="font-medium text-xs uppercase">Reply</span>
                          <MessageCircle size={16} />
                        </HStack>
                      </Show>
                    </HStack>
                  </VStack>
                  <Show when={expandedParentId === item.id}>
                    <Separator color="#0000001A" />
                  </Show>
                  {expandedParentId === item.id &&
                    listChildComment?.data.map((childItem: IComment, childIndex: number) => (
                      <VStack spacing={12} key={childIndex}>
                        <HStack pos={'apart'} noWrap>
                          <HStack spacing={12} noWrap>
                            <div className="h-7 w-7">
                              <Image src={childItem?.user?.avatar ?? ''} alt="" width={32} height={32} className="h-7 w-7 rounded-md" />
                            </div>
                            <VStack className="font-geistMono font-medium text-xs uppercase" spacing={4}>
                              {childItem?.user?.username}
                              <span className="whitespace-nowrap"> {moment(childItem?.created_at).format('hh:mm A DD/MM/YYYY')}</span>
                            </VStack>
                          </HStack>
                          <HStack pos={'apart'}>
                            <HStack spacing={4}>
                              <span
                                className={cn(
                                  'font-geistMono font-medium text-black text-xs',
                                  childItem?.user_reaction_type === 'LOVE' && 'text-red-600'
                                )}
                              >
                                {childItem?.reaction_count}
                              </span>
                              <Icons.heart
                                size={16}
                                className="cursor-pointer"
                                color={childItem?.user_reaction_type === 'LOVE' ? '#FF0000' : '#000000'}
                                onClick={() => {
                                  if (isLoggedIn) {
                                    mutateReaction({
                                      comment_id: childItem?.id,
                                      reaction: childItem?.user_reaction_type === 'LOVE' ? '' : 'LOVE',
                                    });
                                  }
                                }}
                              />
                            </HStack>
                          </HStack>
                        </HStack>
                        <VStack spacing={12}>
                          <p className="font-geistMono font-normal text-sm">{childItem?.comment}</p>
                          <Show when={isLoggedIn}>
                            <HStack align={'end'} pos={'right'}>
                              <HStack
                                spacing={4}
                                className="cursor-pointer"
                                onClick={() => handleReplyClick(item.id, childItem?.user?.username)}
                              >
                                <span className="font-medium text-xs uppercase">Reply</span>
                                <MessageCircle size={16} />
                              </HStack>
                            </HStack>
                          </Show>
                        </VStack>
                      </VStack>
                    ))}
                  {replyingTo === item.id && (
                    <>
                      {' '}
                      <Separator color="#0000001A" />
                      <div className="relative flex items-center rounded-[8px] border border-white/15 bg-[#FFFFFF] px-3 py-2 text-tertiary-900 text-xs">
                        <textarea
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSubmitReply(item.id);
                            }
                          }}
                          placeholder="Leave your words here..."
                          className="max-h-32 flex-1 resize-none overflow-y-auto border-0 bg-transparent p-2 outline-none placeholder:text-[#8A513659] focus:ring-0"
                          rows={1}
                        />
                        <button
                          onClick={() => handleSubmitReply(item.id)}
                          disabled={isReplying}
                          className="absolute top-2 right-2 p-2 transition-transform duration-200 hover:translate-x-0.5"
                        >
                          <Send size={20} color="#583220" />
                        </button>
                      </div>
                    </>
                  )}
                </VStack>
              </VStack>
            ))}
          </React.Fragment>
        ))}
        <div ref={ref} className="flex min-h-[1px] justify-center">
          <NoData
            isFetching={isFetching}
            data={listComment?.pages[0].data}
            Icon={<Icons.noMessage />}
            Title={'No Messages'}
            Paragraph={
              <>
                It’s too quiet… <br />
                Time to take the first shot
              </>
            }
          />
        </div>
      </VStack>
    </div>
  );
};

export default RightChat;
