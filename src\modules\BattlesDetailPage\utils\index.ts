import type { IBattleDetail, IBattleReport } from '@/api/battles/types';
import { addMinutes, isAfter, isDate, parseISO } from 'date-fns';

export function isBattleStillActive(start_at?: string | Date, duration?: number): boolean {
  if (!start_at || !duration) return false;

  const createAtUTC = isDate(start_at) ? start_at : parseISO(start_at);

  const endAtUTC = addMinutes(createAtUTC, duration);
  const nowUTC = new Date();
  return isAfter(endAtUTC, nowUTC);
}

export function isBattleStillBet(start_at?: string | Date, duration?: number): boolean {
  if (!start_at || !duration) return false;

  const createAtUTC = isDate(start_at) ? start_at : parseISO(start_at);

  const endAtUTC = addMinutes(createAtUTC, duration - 5);
  const nowUTC = new Date();
  return isAfter(endAtUTC, nowUTC);
}

export const getAgentWin = (dataReport?: IBattleReport, dataDetail?: IBattleDetail) => {
  if (
    !dataReport ||
    !dataDetail ||
    !dataDetail?.winner_id ||
    (!dataReport?.agent1_data?.current_bet && !dataReport?.agent2_data?.current_bet)
  )
    return undefined;

  if (dataReport?.agent1_data?.current_bet && dataReport?.agent1_data?.agent_id === dataDetail?.winner_id) {
    return dataReport?.agent1_data;
  }
  if (dataReport?.agent2_data?.current_bet && dataReport?.agent2_data?.agent_id === dataDetail?.winner_id) {
    return dataReport?.agent2_data;
  }
  return undefined;
};

export const getAgentLose = (dataReport?: IBattleReport, dataDetail?: IBattleDetail) => {
  if (!dataReport || !dataDetail || !dataDetail?.winner_id) return undefined;

  if (dataReport?.agent2_data?.current_bet && dataReport?.agent1_data?.agent_id === dataDetail?.winner_id) {
    return dataReport?.agent2_data;
  }
  if (dataReport?.agent1_data?.current_bet && dataReport?.agent2_data?.agent_id === dataDetail?.winner_id) {
    return dataReport?.agent1_data;
  }
  return undefined;
};
