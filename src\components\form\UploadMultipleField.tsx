'use client';

import type { MIME_TYPE } from '@/libs/mime';
import type { FieldPath, FieldValues } from 'react-hook-form';

import { HStack } from '../utilities';
import { UploadButtonField } from './UploadButtonField';

type UploadMultipleFieldProps<T extends FieldValues = FieldValues> = {
  form: any;
  name: FieldPath<T>;
  accept: MIME_TYPE[];
};

const UploadMultipleField = ({ form, name, accept }: UploadMultipleFieldProps) => {
  const handleRemoveFile = (index: number) => {
    const arrayImage = form.getValues(name);
    arrayImage.splice(index, 1);
    form.setValue(name, arrayImage as any);
  };
  return (
    <HStack className=" rounded-sm bg-white">
      <div className="px-2">
        <UploadButtonField className="h-12" control={form.control} name={name} accept={accept} />
      </div>
      <div className="flex-1 border-l py-2 pl-4">
        {form.watch(name)?.length >= 1 ? (
          <HStack className="px-4">
            {Array.from(form.watch(name))?.map((file: any, index: number) => (
              <div key={index} className="flex items-center justify-between gap-1 rounded-md bg-gray-100 px-2 py-0.5 shadow-md">
                <span className="select-none px-1 text-xs italic">{file.name} </span>
                <div className="" onClick={() => handleRemoveFile(index)}>
                  {/* <Icons.X className='h-3 w-3 cursor-pointer stroke-red-600 hover:opacity-75' /> */}
                </div>
              </div>
            ))}
          </HStack>
        ) : (
          <div className="mr-5 flex w-full items-center bg-white px-4 text-[#909090] italic">Document Upload</div>
        )}
      </div>
    </HStack>
  );
};

export default UploadMultipleField;
