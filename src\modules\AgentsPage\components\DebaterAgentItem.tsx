'use client';

import type { IAgent } from '@/api/agent/types';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, Show, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

type Props = {
  loading?: boolean;
  className?: string;
} & Partial<IAgent>;

const DebaterAgentItem = ({
  id,
  loading,
  name,
  image_url,
  summary,
  debate_count,
  win_count,
  rate,
  agent_type,
  role,
  className,
  background,
}: Props) => {
  const [isHover, setIsHover] = useState(false);
  return (
    <Link href={`/agents/${String(id)}?type=${agent_type}`}>
      <VStack
        onMouseMove={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
        className={cn('group relative flex gap-6 rounded-xl border border-[#FFFFFF59] bg-[#FFFFFF59]', className)}
      >
        <div
          style={{
            boxShadow: isHover
              ? `0px 185px 65px 0px ${background}05, 0px 118px 60px 0px  ${background}14, 0px 65px 50px 0px ${background}1A, 0px 30px 40px 0px ${background}2E, 0px 7px 20px 0px ${background}33`
              : undefined,
          }}
          className="relative aspect-square w-full rounded-[24px] sm:rounded-[14px] "
        >
          <Image
            src={image_url || '/images/no-image.png'}
            alt="logo"
            width={236}
            height={236}
            className="w-full rounded-[24px] sm:rounded-[14px]"
          />
        </div>

        <VStack className="relative">
          <SkeletonWrapper loading={loading} className="h-4 w-full">
            <span className="font-geistMono text-[#717171] text-xs uppercase">{role || ''}</span>
          </SkeletonWrapper>

          <SkeletonWrapper loading={loading} className="h-4 w-full">
            <span className="font-jersey15 font-normal text-[#131313] text-[2rem] uppercase leading-[normal]">{name}</span>
          </SkeletonWrapper>

          <Show when={agent_type === 'PARENT_AGENT'}>
            <div className="invisible absolute bottom-5 flex items-center font-semibold text-[#131313] text-sm opacity-0 transition-all duration-200 ease-in group-hover:visible group-hover:opacity-100">
              VIEW TRAITS
              <ArrowRight className="ml-2 h-4 w-4" />
            </div>
          </Show>

          <div className="opacity-100 transition-all duration-200 group-hover:invisible group-hover:opacity-0 ">
            <Show when={agent_type === 'PARENT_AGENT'}>
              <SkeletonWrapper loading={loading} className="h-5 w-full">
                <Show when={Number(debate_count) >= 0}>
                  <HStack className=" gap-8 text-sm" noWrap>
                    <VStack className="gap-0">
                      <span className="font-medium text-[#717171] uppercase">Battles</span>
                      <span className="align-middle font-geistMono font-medium text-[#131313] text-sm uppercase leading-[150%] tracking-[0%]">
                        {debate_count}
                      </span>
                    </VStack>

                    <VStack className="gap-0">
                      <span className="font-medium text-[#717171] uppercase">Wins</span>
                      <span className="font-geistMono font-semibold text-[#008050]">
                        {win_count} ({formatNumber(rate, 2)}%)
                      </span>
                    </VStack>
                  </HStack>
                </Show>
              </SkeletonWrapper>
            </Show>
            <Show when={agent_type === 'JUDGE_AGENT'}>
              <SkeletonWrapper loading={loading} className="h-5 w-full">
                <p className="line-clamp-5 font-geistMono font-normal text-[#717171] text-sm uppercase">{summary}</p>
              </SkeletonWrapper>
            </Show>
          </div>
        </VStack>
      </VStack>
    </Link>
  );
};

export default DebaterAgentItem;
