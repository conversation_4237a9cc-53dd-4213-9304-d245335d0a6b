import { cookies } from 'next/headers';
const encodeBase64 = (text: string) => {
  return btoa(text);
};
// to do: remove
export async function POST(request: Request) {
  const cookieStore = await cookies();
  const formData = await request.formData();
  const password = String(formData.get('password'));

  const passEnv = process.env.PASSWORD?.split(',') || [];

  if (passEnv.includes(password)) {
    cookieStore.set({
      name: 'password',
      value: encodeBase64(password),
      expires: Date.now() + 24 * 60 * 60 * 1000,
    });
    return new Response('', {
      status: 200,
    });
  }

  return new Response('', {
    status: 400,
  });
}
