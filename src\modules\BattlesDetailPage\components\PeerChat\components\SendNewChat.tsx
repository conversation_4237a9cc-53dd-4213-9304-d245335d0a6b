import { replyComment } from '@/api/comment/requests';
import { onMutateError } from '@/libs/common';
import { useMutation } from '@tanstack/react-query';
import { Send } from 'lucide-react';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';

const SendNewChat = ({ refetch }: { refetch: () => void }) => {
  const [message, setMessage] = useState('');
  const params = useParams();
  const id = params?.id;

  const { mutate: mutateReply } = useMutation(replyComment, {
    onSuccess: () => {
      setMessage('');
      refetch();
    },
    onError: onMutateError,
  });

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (message.trim()) {
      mutateReply({ battle_id: String(id), comment: message });
      setMessage('');
    }
  };

  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div onClick={handleSubmit} className="relative flex h-[86px] items-center border-white bg-white p-3 text-black text-xs">
      <textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Leave your words here..."
        className="flex-1 resize-none overflow-y-auto border-0 bg-transparent p-2 pr-6 font-geistMono outline-none placeholder:text-[#8A513659] focus:ring-0"
        rows={2}
      />
      <button type="submit" className="absolute top-5 right-2 p-2 transition-transform duration-200 hover:translate-x-0.5">
        <Send size={20} color="#583220" />
      </button>
    </div>
  );
};

export default SendNewChat;
