import { useBattleHistory } from '@/api/agent/queries';
import type { IDataBattleHistory, IParamsBattleHistory } from '@/api/agent/types';
import NoData from '@/components/NoData';
import HeaderAnonymous from '@/components/no-data/HeaderAnonymous';
import { TablePagination } from '@/components/ui/table';
import { Show } from '@/components/utilities';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import CardBattleHistoryAgent from './CardBattleHistoryAgent';

const BattleHistory = () => {
  const params = useParams();
  const id = params?.id;
  const [paramsQuery, setParamsQuery] = useState<IParamsBattleHistory>({
    page: 1,
    limit: 12,
  });
  const { data, isFetching } = useBattleHistory({ variables: { ...paramsQuery, agent_id: String(id) }, enabled: Boolean(id) });

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 md:px-16 xl:grid-cols-3">
        <Show when={!isFetching && data?.data && !!data?.data?.length}>
          {data?.data.map((item, index) => {
            return <CardBattleHistoryAgent key={index} item={item} />;
          })}
        </Show>
        <Show when={isFetching}>
          {Array.from({ length: Number(params.limit || 6) }).map((_, index) => (
            <CardBattleHistoryAgent key={index} item={{} as IDataBattleHistory} loading={isFetching} />
          ))}
        </Show>
      </div>
      <Show when={!isFetching && (!data || data?.data?.length === 0)}>
        <div className="my-8">
          <NoData
            isFetching={isFetching}
            data={data?.data}
            Icon={<HeaderAnonymous />}
            Title={'No Legends Made — Yet'}
            Paragraph={
              <>
                The first epic clash is just <br /> around the corner
              </>
            }
            classNames={{
              title: 'text-[#131313] no-shadow',
              paragraph: 'text-[#717171]',
            }}
          />
        </div>
      </Show>

      <Show when={!isFetching && data?.data?.length !== 0 && !!data}>
        <div className="border-white/15 border-t px-16 py-2">
          <TablePagination
            onPageChange={(page) => setParamsQuery({ ...paramsQuery, page })}
            onPageSizeChange={(limit) => setParamsQuery({ ...paramsQuery, limit: Number(limit) })}
            pagination={{
              ...data?.pagination,
              current_page: paramsQuery.page,
              limit: paramsQuery.limit,
            }}
          />
        </div>
      </Show>
    </>
  );
};

export default BattleHistory;
