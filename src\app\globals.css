@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #000000;
    --primary: #0d6efd;
    --primary-foreground: #7e7e7e;
    --input: #dbdbdb;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* primary */
    --primary-50: #faf7f0;
    --primary-100: #f0e7d5;
    --primary-200: #dfcea8;
    --primary-300: #cfb27a;
    --primary-400: #c39b5c;
    --primary-500: #b98247;
    --primary-600: #a3683c;
    --primary-700: #8a5136;
    --primary-800: #704130;
    --primary-900: #5d362a;

    /* Secondary */
    --secondary-50: #fbf7f1;
    --secondary-100: #f7ebdd;
    --secondary-200: #eed5ba;
    --secondary-300: #e2b78f;
    --secondary-400: #d38c58;
    --secondary-500: #cc7743;
    --secondary-600: #be6238;
    --secondary-700: #9e4d30;
    --secondary-800: #7f3f2d;
    --secondary-900: #673527;

    /* Tertiary */
    --tertiary-50: #faf7ec;
    --tertiary-100: #f3eace;
    --tertiary-200: #e9d59f;
    --tertiary-300: #ddb867;
    --tertiary-400: #d19e3e;
    --tertiary-500: #c28830;
    --tertiary-600: #a76b27;
    --tertiary-700: #864e22;
    --tertiary-800: #704023;
    --tertiary-900: #583220;

    /* Gray */
    --gray-50: #f8f6f4;
    --gray-100: #eeebe6;
    --gray-200: #ddd5cb;
    --gray-300: #d1c5b9;
    --gray-400: #b09987;
    --gray-500: #a0826f;
    --gray-600: #937363;
    --gray-700: #7a5f54;
    --gray-800: #644f48;
    --gray-900: #52413c;

    /* Shamrock */
    --shamrock-50: #ebfef4;
    --shamrock-100: #cefde1;
    --shamrock-200: #a2f8c9;
    --shamrock-300: #66efaf;
    --shamrock-400: #38e096;
    --shamrock-500: #05c477;
    --shamrock-600: #00a061;
    --shamrock-700: #008050;
    --shamrock-800: #006541;
    --shamrock-900: #015337;

    /* Amaranth */
    --amaranth-50: #fff1f1;
    --amaranth-100: #fee6e5;
    --amaranth-200: #fdcecf;
    --amaranth-300: #fba6a8;
    --amaranth-400: #f7757a;
    --amaranth-500: #ef444f;
    --amaranth-600: #e0384b;
    --amaranth-700: #b9172d;
    --amaranth-800: #9b162d;
    --amaranth-900: #85162c;

    /* Red Damask */
    --red-damask-50: #fff1f1;
    --red-damask-100: #fae9da;
    --red-damask-200: #f4d0b4;
    --red-damask-300: #edb084;
    --red-damask-400: #e58652;
    --red-damask-500: #e06d38;
    --red-damask-600: #d05026;
    --red-damask-700: #ad3d21;
    --red-damask-800: #8a3222;
    --red-damask-900: #702b1e;

    /* dark */
    --dark-50: #f5f5f5;
    --dark-100: #131313;
    --dark-200: #101010;
    --dark-300: #0d0d0d;
    --dark-400: #0a0a0a;
    --dark-500: #080808;
    --dark-600: #717171;
    --dark-700: #040404;
    --dark-800: #020202;
  }
  /* .dark {
    --radius: 0.5rem;
    --background: #000000;
    --foreground: #ffffff;
    --primary: #0d6efd;
    --primary-foreground: #7e7e7e;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: #dbdbdb;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  } */
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html,
  body {
    @apply scroll-smooth font-normal text-black;
  }
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar {
  width: 6px;
  border-radius: 10px;
  background-color: #fff;
  overflow-y: overlay;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: #424242;
}

.cursor-blink {
  display: inline-block;
  width: 10px;
  height: 1.5em;
  background-color: black;
  animation: blink 1s step-start infinite;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}
.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-shadow {
  text-shadow: none !important;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}
