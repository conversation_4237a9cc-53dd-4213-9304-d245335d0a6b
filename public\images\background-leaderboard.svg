<svg width="1440" height="1352" viewBox="0 0 1440 1352" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8" filter="url(#filter0_f_6_1625)">
<g clip-path="url(#paint0_diamond_6_1625_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0 -0.449222 0.6208 0 720 676)"><rect x="0" y="0" width="1284.44" height="1283.83" fill="url(#paint0_diamond_6_1625)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1284.44" height="1283.83" transform="scale(1 -1)" fill="url(#paint0_diamond_6_1625)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1284.44" height="1283.83" transform="scale(-1 1)" fill="url(#paint0_diamond_6_1625)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1284.44" height="1283.83" transform="scale(-1)" fill="url(#paint0_diamond_6_1625)" opacity="1" shape-rendering="crispEdges"/></g></g><rect x="-76" y="100" width="1592" height="1152" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.81960785388946533,&#34;b&#34;:0.0039215688593685627,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.87450981140136719,&#34;g&#34;:0.17647059261798859,&#34;b&#34;:0.0039215688593685627,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.81960785388946533,&#34;b&#34;:0.0039215688593685627,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.87450981140136719,&#34;g&#34;:0.17647059261798859,&#34;b&#34;:0.0039215688593685627,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-3.1511570696557412e-12,&#34;m01&#34;:1241.5997314453125,&#34;m02&#34;:99.200180053710938,&#34;m10&#34;:-898.44403076171875,&#34;m11&#34;:1.5082664934704115e-12,&#34;m12&#34;:1125.2220458984375},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<filter id="filter0_f_6_1625" x="-176" y="0" width="1792" height="1352" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_6_1625"/>
</filter>
<clipPath id="paint0_diamond_6_1625_clip_path"><rect x="-76" y="100" width="1592" height="1152"/></clipPath><linearGradient id="paint0_diamond_6_1625" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD101"/>
<stop offset="1" stop-color="#DF2D01" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
