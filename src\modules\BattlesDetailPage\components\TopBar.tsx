import { useBattleById, useYourBets } from '@/api/battles/queries';
import Tabs from '@/components/TabsV2';
import { Show } from '@/components/utilities';
import useCheckTimeOverBattle from '@/hooks/useCheckTimeOverBattle';
import { useUserLogin } from '@/hooks/useUserLogin';
import { onMutateError } from '@/libs/common';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import FlowChannel from './FlowChannel';
import YourBets from './YourBets';

const TopBar = () => {
  const [tab, setTab] = useState<string | number>('BET');
  const { isLoggedIn } = useUserLogin();

  const { id } = useParams();
  const { data } = useYourBets({ variables: String(id), enabled: Boolean(id && isLoggedIn), onError: onMutateError });
  const { data: battle, isFetching } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });
  const { isBattleStillActive } = useCheckTimeOverBattle({
    duration: battle?.duration,
    start_at: battle?.start_at,
  });

  return (
    <section className="md:p-4 lg:p-6">
      <Tabs
        itemClassName="flex-1 overflow-hidden"
        tabs={tab}
        setTabs={setTab}
        data={[
          { label: 'BET', value: 'BET' },
          {
            label: `Your Bets (${data?.length || 0})`,
            value: 'YOUR_BET',
            disabled: !isLoggedIn || !data?.length,
            tooltip: !isLoggedIn ? (
              <>
                Connect your wallet to place <br /> or see your bets
              </>
            ) : undefined,
          },
        ]}
        className="text-xs"
      />

      <Show when={tab === 'BET'}>
        <FlowChannel />
      </Show>

      <Show when={tab === 'YOUR_BET'}>
        <YourBets onButtonClick={() => setTab('BET')} />
      </Show>
    </section>
  );
};

export default TopBar;
