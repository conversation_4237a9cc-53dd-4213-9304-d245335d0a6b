import type { IBattleDetail } from '@/api/battles/types';
import { HStack, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import Image from 'next/image';

const BattleFinished = ({ data }: { data: IBattleDetail | undefined }) => {
  return (
    <VStack className="items-center justify-center gap-6 py-2">
      <Image src={'/images/battle-finish.png'} width={200} height={200} alt="" />
      <div className="relative">
        <HStack pos={'apart'} noWrap className="-space-x-10 w-full items-center justify-center self-stretch">
          <div className={cn('-rotate-[7deg] rounded-xl')}>
            <img src={data?.agent1?.image_url || '/images/no-image.png'} width={136} height={136} alt="" className="h-[136px] rounded-xl" />
          </div>
          <div className={cn('rotate-[7deg] rounded-xl')}>
            <img src={data?.agent2?.image_url || '/images/no-image.png'} width={136} height={136} alt="" className="h-[136px] rounded-xl" />
          </div>
        </HStack>
        <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-[45%]">
          <img src="/images/vss.png" alt="" width={40} height={40} />
        </div>
      </div>
      <div className="flex h-7 w-fit items-center justify-center rounded-[9px] bg-[#131313] px-3 py-2 font-geistMono font-semibold text-green-500 text-xs">
        <p>{data?.winner?.name} WINS</p>
      </div>
    </VStack>
  );
};

export default BattleFinished;
