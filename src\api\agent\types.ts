import type { IPagination } from '@/types';

export interface ICriteriaAgent {
  charisma_and_presence: number;
  credibility_and_execution: number;
  emotional_impact: number;
  logic_and_reasoning: number;
  tactical_skills: number;
  vision_and_innovation: number;
}

export interface ICriteriaJudge {
  audience_empathy: number;
  creativity_appreciation: number;
  ethical_sensitivity: number;
  logical_analysis: number;
  objectivity: number;
  practicality_feasibility: number;
}
export interface IAgent {
  id: string;
  name: string;
  age: string;
  capabilities: string[];
  description: string;
  social_url: string;
  image_url: string;
  agent_type: string;
  created_at: string;
  updated_at: string;
  debate_count: number;
  win_count: number;
  rate: number;
  metrics: IMetric[];
  role: string;
  summary: string;
  criteria?: ICriteriaAgent | ICriteriaJudge;
  attributes: IAttributeAgent[];
  banner_url: string;
  background: string;
}

export interface IAttributeAgent {
  key: string;
  options: IOptionAttribute[];
}

export interface IOptionAttribute {
  id: number;
  metric: string;
  title: string;
  properties: string;
}

export interface IMetric {
  id: string;
  agent_id: string;
  metric: string;
  properties: string[];
  created_at: string;
  updated_at: string;
}

export interface IParamsBattleChart {
  agent_id?: string;
  start_date?: string;
  end_date?: string;
}

export interface IParamsBattleHistory {
  page?: number;
  limit?: number;
  agent_id?: string;
  start_date?: string;
  end_date?: string;
}

export interface IResponseBattleChart {
  total_bet_amount: number;
  data: {
    date: string;
    total_bet_amount: number;
    agent_bet: number;
    opponent_bet: number;
    total_battle: number;
    total_battle_won: number;
  }[];
}

export interface IResponseBattleHistory {
  pagination: IPagination;
  data: IDataBattleHistory[];
}

export interface IDataBattleHistory {
  id: number;
  topic: string;

  start_at: string;
  duration: number;
  agent1: IAgent & { result: 'WON' | 'LOST' | 'Pending' };
  agent2: IAgent & { result: 'WON' | 'LOST' | 'Pending' };
  status: 'WON' | 'LOST' | 'Pending';
  voted_by: { name: string; image_url: string; id: number }[];
  slug: string;
}

export interface IResponseJudgeHistory {
  pagination: IPagination;
  data: IDataJudgeHistory[];
}

export interface IDataJudgeHistory {
  id: number;
  topic: string;
  agent1: IAgent & { result: 'WON' | 'LOST' | 'Pending' };
  agent2: IAgent & { result: 'WON' | 'LOST' | 'Pending' };
  start_at: string;
  total_bet_amount: number;
  agent1_status: string;
  agent2_status: string;
  vote_for_agent: {
    id: string;
    battle: string;
    agent: string;
    vote_agent_name: string;
    comment: string;
    voted_agent: string;
  };
  slug: string;
}
