import type { Address } from 'viem';

export const env = {
  isProduction: process.env.NODE_ENV === 'production',
  APP_URL: process.env.NEXT_PUBLIC_APP_URL ?? 'https://staging.og-battle-ai.var-meta.com',
  API_URL: process.env.NEXT_PUBLIC_API_URL ?? '',
  APP_SOCKET_URL: process.env.NEXT_PUBLIC_APP_SOCKET_URL ?? '',
  TWITTER_CLIENT_ID: process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID ?? '',
  TWITTER_CLIENT_SECRET: process.env.NEXT_PUBLIC_TWITTER_CLIENT_SECRET ?? '',
  CHAIN_ID: process.env.NEXT_PUBLIC_CHAIN_ID ? Number(process.env.NEXT_PUBLIC_CHAIN_ID) : 16601,
  CHAIN_RPC_URL: process.env.NEXT_PUBLIC_CHAIN_RPC_URL ?? 'https://evmrpc-testnet.0g.ai',
  SIGN_MESSAGE_WALLET: process.env.NEXT_PUBLIC_SIGN_MESSAGE_WALLET ?? '',
};

export const LAST_MONTH = new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000);

export const isServer = typeof window === 'undefined';
export const useTestnet = process.env.NEXT_PUBLIC_USE_TESTNET! ? process.env.NEXT_PUBLIC_USE_TESTNET === 'true' : true;

export const AVATAR_PROFILE_RANDOMS = [
  // 'https://battle-of-ai-agents.s3.amazonaws.com/dev/avatar_default/Gj_k4qzWUAAGg0C_1741230716.webp',
  // 'https://battle-of-ai-agents.s3.amazonaws.com/dev/avatar_default/Gj_k4qzWUAAGg0C_1741230807.webp',
  // 'https://battle-of-ai-agents.s3.amazonaws.com/dev/avatar_default/GkRJm9LXUAA2D2P_1741231696.webp',
  // 'https://battle-of-ai-agents.s3.amazonaws.com/dev/avatar_default/GkKnrvsXIAAIZTM_1741231697.png',
  // 'https://battle-of-ai-agents.s3.amazonaws.com/dev/avatar_default/GkkwOQnX0AAk_yS_1741231696.webp',
  'https://var-meta.oss-eu-central-1.aliyuncs.com/production/avatar/GkkwOQnX0AAk_yS_1741231696_1749711133.webp',
  'https://var-meta.oss-eu-central-1.aliyuncs.com/production/avatar/GkKnrvsXIAAIZTM_1741231697_1749711133.png',
  'https://var-meta.oss-eu-central-1.aliyuncs.com/production/avatar/GkRJm9LXUAA2D2P_1741231696_1749711133.webp',
  'https://var-meta.oss-eu-central-1.aliyuncs.com/production/avatar/Gj_k4qzWUAAGg0C_1741230807_1749711133.webp',
];

export const SMART_CONTRACT_ADDRESS = (process.env.NEXT_PUBLIC_SMART_CONTRACT_ADDRESS ??
  '0xA1ef2b6d1B4cd00324Cc8BFD4F6B521418aCf1cA') as Address;
