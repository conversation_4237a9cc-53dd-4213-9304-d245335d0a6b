import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { create } from 'zustand';

export const TabBattleMobileData = {
  OVERVIEW: 'Overview',
  BATTLE: 'Battle',
  PEER_CHAT: 'Peer Chat',
  TOP_BETTORS: 'Top Bettors',
  ACTIVITY: 'Activity',
} as const;

type TabBattleMobileType = typeof TabBattleMobileData;

export const ListTableBattleDetail = (
  Object.entries(TabBattleMobileData) as [keyof TabBattleMobileType, TabBattleMobileType[keyof TabBattleMobileType]][]
).map(([key, label]) => ({
  label,
  value: key,
}));

export interface TabBattleDetail {
  tab: keyof TabBattleMobileType;
  setTab: (value: keyof TabBattleMobileType) => void;
}

const useMobileBaseTabBattleStore = create<TabBattleDetail>()((set) => ({
  tab: ListTableBattleDetail[0].value,
  setTab: (value: keyof TabBattleMobileType) => set((state) => ({ ...state, tab: value })),
}));

export const useMobileTabBattleStore = createSelectorFunctions(useMobileBaseTabBattleStore);
