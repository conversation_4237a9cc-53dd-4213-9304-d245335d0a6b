import { useBattleById } from '@/api/battles/queries';
import { Icons } from '@/assets/icons';
import H3 from '@/components/text/H3';
import { HStack, VStack } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useMemo } from 'react';

type Props = {
  setAgentSelected: React.Dispatch<React.SetStateAction<{ label: string; value: string } | undefined>>;
  agentSelected: any;
};
const BetOn = ({ setAgentSelected, agentSelected }: Props) => {
  const { id } = useParams();

  const { data, isFetching } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });

  const listAgents = useMemo(() => {
    if (!data) return [];

    return [
      {
        label: data?.agent1?.name,
        value: String(data?.agent1?.id),
        image: data?.agent1?.image_url,
      },
      {
        label: data?.agent2?.name,
        value: String(data?.agent2?.id),
        image: data?.agent2?.image_url,
      },
    ];
  }, [data]);

  return (
    <div className="">
      <div className="mb-3 font-medium text-dark-100 text-sm">BET ON</div>

      <VStack spacing={16}>
        {listAgents.map((item, index) => (
          <HStack
            className={cn('cursor-pointer rounded-[8px] border border-dark-100 bg-white p-1 hover:bg-[#FFEB00]', {
              'border-2 border-[#FF3901] bg-[#FFEB00]': agentSelected?.value === item.value,
            })}
            key={item.value}
            spacing={16}
            noWrap
            onClick={() => setAgentSelected(item)}
          >
            <Image src={item.image} alt="" width={48} height={48} className="h-12 w-12 rounded-md" />

            <H3 className="flex-1 font-semibold text-dark-100 text-sm lg:text-base">{item.label}</H3>

            {agentSelected?.value === item.value && <Icons.done className="mr-2" />}
          </HStack>
        ))}
      </VStack>
    </div>
  );
};

export default BetOn;
