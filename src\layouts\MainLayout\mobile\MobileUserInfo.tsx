'use client';

import { Icons } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import MyProfile from '@/components/user/MyProfile';
import { HStack, VStack } from '@/components/utilities';
import { useCopy } from '@/hooks/useCopy';
import { useUserLogin } from '@/hooks/useUserLogin';
import { shortenAddress } from '@/libs/utils';
import { useUserStore } from '@/stores/UserStore';
import { useQueryClient } from '@tanstack/react-query';
import { deleteCookie } from 'cookies-next';
import { Check } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useDisconnect } from 'wagmi';

const MobileUserInfo = () => {
  const { user, address } = useUserLogin();
  const { logout } = useUserStore();
  const { disconnect } = useDisconnect();
  const queryClient = useQueryClient();
  const [copied, copy] = useCopy();
  const router = useRouter();

  const handleLogout = () => {
    disconnect();
    logout();
    router.refresh();
    queryClient.clear();
    deleteCookie('access_token');
    deleteCookie('refresh_token');
  };
  return (
    <VStack spacing={16} className="mt-6 w-full sm:w-auto">
      <HStack spacing={16}>
        <Image src={user?.avatar || '/avatars/dog.png'} alt="Avatar" width={72} height={72} className="h-14 w-14 rounded-sm" />

        <VStack spacing={4} className="text-dark-100 text-sm">
          <span className="font-jersey15 text-[32px]">{user?.username || '--'}</span>
          <div className="flex items-center space-x-4">
            <span className="font-semibold">{address ? shortenAddress(String(address), 4) : '--'}</span>
            <button className="hover:opacity-75" onClick={() => copy(address)}>
              {!copied ? <Icons.copy /> : <Check className="w-6" />}
            </button>
          </div>
        </VStack>
      </HStack>

      <HStack className="w-full" spacing={8}>
        <Button className="border border-[#131313] text-sm shadow-none" onClick={handleLogout}>
          Logout
        </Button>

        <MyProfile>
          <Button className="flex-1 border border-[#131313] text-sm shadow-none">Edit profile</Button>
        </MyProfile>
      </HStack>
    </VStack>
  );
};

export default MobileUserInfo;
