import type { IAgent } from '@/api/agent/types';
import { useBattleById, useBattleInfoById } from '@/api/battles/queries';
import type { IAgentReport } from '@/api/battles/types';
import H2 from '@/components/text/H2';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, VStack } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import { formatNumber } from '@/libs/utils';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import BattleBottom from '../components/BattleStats/BattleBottom';

const MobileBattleStats = () => {
  const { id } = useParams();
  const { data, refetch, isFetching } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });
  const { data: dataReport } = useBattleInfoById({ variables: String(id), enabled: Boolean(id), onError: onMutateError });

  const agent1 = data?.agent1;
  const agent2 = data?.agent2;

  const getAgentData = (agent?: IAgent): IAgentReport | undefined => {
    if (agent?.id === dataReport?.agent1_data?.agent_id) {
      return dataReport?.agent1_data;
    } else if (agent?.id === dataReport?.agent2_data?.agent_id) {
      return dataReport?.agent2_data;
    }
    return undefined;
  };

  const agent1Report = getAgentData(agent1);
  const agent2Report = getAgentData(agent2);
  return (
    <div className="relative h-[580px] overflow-hidden rounded-[36px]">
      <Image
        priority
        src={
          data?.is_resolved && data?.winner_id
            ? data?.banner_debate_end_url || '/images/battle-end.png'
            : data?.banner_url || '/images/battle-active.png'
        }
        alt="background"
        fill
        className="-z-[1] h-full w-full object-cover"
      />
      <div className="-z-[1] pointer-events-none absolute inset-0 bg-[radial-gradient(50%_50%_at_50%_50%,rgba(0,0,0,0)_0%,rgba(0,0,0,0.4)_100%)]" />

      <VStack className="z-[1] mt-16 items-center justify-center gap-4 xl:gap-24">
        <HStack pos={'apart'} noWrap className="-space-x-6 w-full items-center justify-center self-stretch">
          <SkeletonWrapper loading={isFetching}>
            <div className="-rotate-[7deg]">
              <Image
                src={data?.agent1?.image_url || '/images/no-image.png'}
                width={120}
                height={120}
                alt="agent1"
                className={cn('h-[120px] w-[120px] rounded-xl bg-gray-300 object-cover object-top', {
                  'opacity-25 brightness-150 grayscale': !!data?.is_resolved && !!data?.winner_id && data?.agent2?.id === data?.winner_id,
                })}
              />
              <div className="absolute inset-0 bg-black/10" />
            </div>
          </SkeletonWrapper>
          <SkeletonWrapper loading={isFetching}>
            <div className="rotate-[7deg]">
              <Image
                src={data?.agent2?.image_url || '/images/no-image.png'}
                width={120}
                height={120}
                alt="agent2"
                className={cn('h-[120px] w-[120px] rounded-xl bg-gray-300 object-cover object-top', {
                  'opacity-25 brightness-150 grayscale': !!data?.is_resolved && !!data?.winner_id && data?.agent1?.id === data?.winner_id,
                })}
                style={{
                  backgroundColor: 'lightgray',
                }}
              />
              <div className="absolute inset-0 bg-black/10" />
            </div>
          </SkeletonWrapper>
          <div className="-translate-x-[20%] absolute left-1/2 h-[46px] w-[54px]">
            <Image src="/images/vs.png" alt="logo" fill sizes="100vh" className="-rotate-[5deg]" />
          </div>
        </HStack>
        <VStack className="items-center justify-center">
          <H2 className="overflow-hidden text-ellipsis text-center font-jersey15 font-normal text-[32px] text-white uppercase leading-[normal]">
            {data?.agent1?.name || ''}
          </H2>
          <div className="text-center font-semibold text-[#FFFFFF] text-sm uppercase">
            Total bets: {formatNumber(Number(agent1Report?.total_bet), Number(agent1Report?.total_bet) >= 100 ? 2 : 5)}
          </div>

          <H2 className="overflow-hidden text-ellipsis text-center font-jersey15 font-normal text-[32px] text-white uppercase leading-[normal]">
            {data?.agent2?.name || ''}
          </H2>
          <div className="text-center font-semibold text-[#FFFFFF] text-sm uppercase">
            Total bets: {formatNumber(Number(agent2Report?.total_bet), Number(agent2Report?.total_bet) >= 100 ? 2 : 5)}
          </div>
        </VStack>
      </VStack>

      <BattleBottom data={data} stats={dataReport?.total} />
    </div>
  );
};

export default MobileBattleStats;
