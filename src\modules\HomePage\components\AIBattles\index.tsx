import { useListBattle } from '@/api/battles/queries';
import type { IDataBattleDashboard } from '@/api/battles/types';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { HStack, Show } from '@/components/utilities';
import Link from 'next/link';
import { useRef } from 'react';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { NavigationOptions } from 'swiper/types';
import WrapperSectionHome from '../WrapperSectionHome';
import CardBattles from './CardBattles';

const AIBattles = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const { data, isFetching } = useListBattle({
    variables: {
      page: 1,
      limit: 10,
    },
  });
  return (
    <WrapperSectionHome className="!gap-12">
      <HStack pos="apart" noWrap>
        <H1 className="text-ellipsis text-black leading-[140%]">AI Battles</H1>
        <Link href={'/battles'}>
          <Button className="">View All</Button>
        </Link>
      </HStack>
      <div className="relative grid w-full grid-cols-1 items-center justify-center lg:grid-cols-2 xl:grid-cols-3">
        {/* to do: keep ! */}
        {/* <button
              ref={prevRef}
              className="absolute left-[-25px] z-10 rounded-sm border border-white/15 bg-white/15 p-2 text-white backdrop-blur-md"
            >
              <ChevronLeft size={24} />
            </button> */}
        {/* <button
              ref={nextRef}
              className="absolute right-[-25px] z-10 rounded-sm border border-white/15 bg-white/15 p-2 text-white backdrop-blur-md"
            >
              <ChevronRight size={24} />
            </button> */}
        <Show when={!isFetching && (data?.data || []).length > 2}>
          {/* <Swiper
                // to do: enabled = true
                enabled={false}
                modules={[Navigation]}
                spaceBetween={20}
                navigation={{
                  prevEl: prevRef.current,
                  nextEl: nextRef.current,
                }}
                loop
                breakpoints={{
                  375: { slidesPerView: 1 },
                  463: { slidesPerView: 1.08 },
                  660: { slidesPerView: 1.5 },
                  791: { slidesPerView: 1.8 },
                  871: { slidesPerView: 2 },
                  919: { slidesPerView: 2.1 },
                  1005: { slidesPerView: 2.3 },
                  1093: { slidesPerView: 2.5 },
                  1175: { slidesPerView: 2.7 },
                  1234: { slidesPerView: 2.8 },
                  1278: { slidesPerView: 2.9 },
                  1312: { slidesPerView: 3 },
                }}
                onInit={(swiper) => {
                  setTimeout(() => {
                    if (swiper.params.navigation) {
                      (swiper.params.navigation as NavigationOptions).prevEl = prevRef.current;
                      (swiper.params.navigation as NavigationOptions).nextEl = nextRef.current;
                      swiper.navigation.init();
                      swiper.navigation.update();
                    }
                  });
                }}
              > */}
          {data?.data?.slice(0, 3).map((item, index) => (
            // <SwiperSlide key={index} className="!h-auto">
            <CardBattles item={item} key={index} />
            // </SwiperSlide>
          ))}
          {/* </Swiper> */}
        </Show>
        <Show when={!isFetching && (data?.data || []).length <= 2}>
          <>
            {data?.data?.map((item, index) => (
              <CardBattles item={item} key={index} />
            ))}
          </>
        </Show>
        <Show when={isFetching}>
          <Swiper
            // to do: enabled = true
            enabled={false}
            modules={[Navigation]}
            spaceBetween={20}
            navigation={{
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            }}
            loop
            breakpoints={{
              375: { slidesPerView: 1 },
              463: { slidesPerView: 1.08 },
              660: { slidesPerView: 1.5 },
              791: { slidesPerView: 1.8 },
              871: { slidesPerView: 2 },
              919: { slidesPerView: 2.1 },
              1005: { slidesPerView: 2.3 },
              1093: { slidesPerView: 2.5 },
              1175: { slidesPerView: 2.7 },
              1234: { slidesPerView: 2.8 },
              1278: { slidesPerView: 2.9 },
              1312: { slidesPerView: 3 },
            }}
            onInit={(swiper) => {
              setTimeout(() => {
                if (swiper.params.navigation) {
                  (swiper.params.navigation as NavigationOptions).prevEl = prevRef.current;
                  (swiper.params.navigation as NavigationOptions).nextEl = nextRef.current;
                  swiper.navigation.init();
                  swiper.navigation.update();
                }
              });
            }}
          >
            {Array.from({ length: 5 }).map((_, index) => (
              <SwiperSlide key={index}>
                <CardBattles item={{} as IDataBattleDashboard} loading={isFetching} />
              </SwiperSlide>
            ))}
          </Swiper>
        </Show>
      </div>
    </WrapperSectionHome>
  );
};

export default AIBattles;
