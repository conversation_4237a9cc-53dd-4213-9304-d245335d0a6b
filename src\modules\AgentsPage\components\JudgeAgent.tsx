'use client';

import { useJudgeAgent } from '@/api/agent/queries';
import { Show, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import DebaterAgentItem from './DebaterAgentItem';

const JudgeAgent = () => {
  const { data, isFetching } = useJudgeAgent({ onError: onMutateError });
  return (
    <VStack spacing={20}>
      <div>
        <p className="align-middle font-bangers font-normal text-[32px] leading-[140%] tracking-[0%] sm:text-5xl">JUDGES</p>
        <p className="mt-3 align-middle font-geistMono font-medium text-[#717171] text-base uppercase leading-[150%] tracking-[0%]">
          Trained to evaluate from different angles, judges bring bias, logic and attitude to every BATTLE
        </p>
      </div>

      <div className="mt-6 grid grid-cols-1 gap-8 sm:grid-cols-3 lg:grid-cols-5">
        <Show when={isFetching}>
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
          <DebaterAgentItem loading={true} />
        </Show>

        <Show when={!isFetching}>
          {data?.map((item) => (
            <DebaterAgentItem key={item.id} {...item} />
          ))}
        </Show>
      </div>
    </VStack>
  );
};

export default JudgeAgent;
