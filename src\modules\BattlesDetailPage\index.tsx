'use client';

import { useBattleById } from '@/api/battles/queries';
import NotFound from '@/components/404';
import Tabs from '@/components/TabsV2';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { HStack, Show, VStack } from '@/components/utilities';
import Container from '@/components/wrapper/Container';
import useMobile from '@/hooks/useMobile';
import { cn } from '@/libs/common';
import { ListTableBattleDetail, useMobileTabBattleStore } from '@/stores/useMobileTabBattleStore';
import { useParams } from 'next/navigation';
import TopBettors from './TopBettors/TopBettors';
import Activity from './components/Activity';
import BattleStats from './components/BattleStats';
import BottomBar from './components/BottomBar';
import ButtonShare from './components/ButtonShare';
import ChatChanel from './components/ChatChannel';
import ListUserBets from './components/ListUserBets';
import PeerChat from './components/PeerChat/PeerChat';
import TopBar from './components/TopBar';
import MobileBattleStats from './mobile/MobileBattleStats';
import MobilePlaceBet from './mobile/MobilePlaceBet';

const BattlesDetail = () => {
  const { id } = useParams();
  const isMobile = useMobile();
  const { data, isFetching, refetch } = useBattleById({ variables: String(id), enabled: !!id });

  const { setTab, tab } = useMobileTabBattleStore();

  if (!data && !isFetching) return <NotFound />;

  return (
    <VStack className="h-full w-full" spacing={0}>
      <Show when={isMobile}>
        <div className="sticky top-0 z-10 w-full bg-white py-2">
          <Tabs tabs={tab} setTabs={setTab as any} data={ListTableBattleDetail} className="border-0 text-xs" />
        </div>
      </Show>
      <Container className="pt-0 lg:pt-0">
        <div className="grid grid-cols-1 pb-[100px] md:gap-8 md:pb-0 lg:gap-16 xl:grid-cols-[1fr_387px]">
          <section
            className={cn('md:mt-10', {
              'my-6': tab === 'OVERVIEW',
            })}
          >
            <Show when={!isMobile || (isMobile && tab === 'OVERVIEW')}>
              <HStack pos="apart" noWrap={!isMobile} spacing={32}>
                <H1>{data?.topic}</H1>

                <ButtonShare
                  {...data}
                  bottomBar={
                    <p className="mt-1 max-w-[145px] text-center font-semibold text-[#FFFFFF] text-[8px] md:mt-3 lg:mt-6 lg:text-xxs">
                      Watch AI go head-to-head in epic 1V1 BATTLES
                    </p>
                  }
                >
                  <Button size="sm">Share</Button>
                </ButtonShare>
              </HStack>
              <p className="my-6 line-clamp-3 font-medium text-[#00000099] text-base uppercase leading-[150%] md:mb-16">{data?.content}</p>

              <Show when={!isMobile}>
                <BattleStats />
              </Show>

              <Show when={isMobile}>
                <MobileBattleStats />
              </Show>
            </Show>

            <Show when={!isMobile || (isMobile && tab === 'BATTLE')}>
              <ChatChanel data={data} refetch={refetch} />
            </Show>
          </section>

          <Show when={!isMobile}>
            <section className="flex h-full flex-col border-[#E5E5E5] border-l bg-[#F5F5F5]">
              <TopBar />

              <Show when={!data?.is_resolved}>
                <ListUserBets />
              </Show>
              <Separator className="border-[#E5E5E5]" />

              <BottomBar />
            </section>
          </Show>

          {/* Mobile */}
          <Show when={isMobile && tab === 'PEER_CHAT'}>
            <PeerChat />
          </Show>
          <Show when={isMobile && tab === 'TOP_BETTORS'}>
            <TopBettors />
          </Show>
          <Show when={isMobile && tab === 'ACTIVITY'}>
            <Activity />
          </Show>

          <Show when={isMobile}>
            <MobilePlaceBet />
          </Show>
        </div>{' '}
      </Container>
    </VStack>
  );
};

export default BattlesDetail;
