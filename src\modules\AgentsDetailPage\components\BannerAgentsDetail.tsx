'use client';

import { useAgentById } from '@/api/agent/queries';
import Header from '@/layouts/MainLayout/Header';
import { onMutateError } from '@/libs/common';
import { useParams } from 'next/navigation';

const BannerAgentsDetail = () => {
  const { id } = useParams();

  const { data, isFetching } = useAgentById({
    variables: String(id),
    onError: onMutateError,
    enabled: Boolean(id),
  });

  return (
    <div>
      <Header className="relative top-0 left-0 border-none bg-transparent backdrop-blur-none [&_a]:text-[#FFF]" />
      <div
        className="-mt-[148px] relative flex h-[400px] flex-col items-end justify-center bg-cover bg-top font-geistMono sm:aspect-[144/40] sm:h-auto sm:bg-top"
        style={{
          background: `linear-gradient(180deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0) 50%), url(${data?.banner_url}) lightgray 50% / cover no-repeat`,
        }}
      ></div>
    </div>
  );
};

export default BannerAgentsDetail;
