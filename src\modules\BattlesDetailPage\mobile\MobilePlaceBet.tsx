import { Button } from '@/components/ui/button';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { useDisclosure } from '@mantine/hooks';
import TopBar from '../components/TopBar';

const MobilePlaceBet = () => {
  const [opened, { toggle }] = useDisclosure(false);

  return (
    <>
      <div className="fixed right-0 bottom-0 left-0 z-20 rounded-tl-[24px] rounded-tr-[24px] bg-[#F3F3F3] p-6">
        <Button className="h-[46px] w-full px-3 py-1.5 font-geistMono font-semibold text-base text-black uppercase" onClick={toggle}>
          Place Bet
        </Button>
      </div>
      <Sheet open={opened} onOpenChange={toggle}>
        <SheetContent className="rounded-tl-[24px] rounded-tr-[24px] bg-white" side="bottom" isBtnClose={false}>
          <TopBar />
        </SheetContent>
      </Sheet>
    </>
  );
};

export default MobilePlaceBet;
