import type { IBattleDetail } from '@/api/battles/types';
import H2 from '@/components/text/H2';
import H4 from '@/components/text/H4';
import { Button } from '@/components/ui/button';
import { Show } from '@/components/utilities';
import { useUserLogin } from '@/hooks/useUserLogin';
import { ROUTER } from '@/libs/router';
import Link from 'next/link';

const VoteRefund = ({ refunded_onchain_status }: Partial<IBattleDetail>) => {
  const { isLoggedIn } = useUserLogin();

  return (
    <div>
      <div className="my-16">
        <H2 className="text-center font-je font-jersey15 text-3xl text-[#FF3901] [text-shadow:_1px_2px_0px_#FFEB00,_2px_4px_0px_#FFEB00] lg:text-[45px]">
          {refunded_onchain_status === 'Completed' ? 'REFUND SUCCESSFUL!' : 'WAITING FOR REFUND!'}
        </H2>

        <H4 className="mt-5 text-center font-geistMono font-semibold text-dark-100 uppercase lg:text-sm">
          {refunded_onchain_status === 'Completed' ? 'The battle encountered an error and has been canceled' : 'DECISION WILL BE MADE SOON'}
        </H4>

        <p className="mt-5 text-center font-geistMono font-medium text-dark-100 text-xxs uppercase lg:text-xs">
          {refunded_onchain_status === 'Completed' ? 'Your bet has been refunded to your wallet.' : 'Your bet will be refunded shortly'}
        </p>
      </div>

      <Show when={isLoggedIn}>
        <Link href={ROUTER.BATTLES}>
          <Button className="w-full" disabled={!isLoggedIn}>
            view next battles
          </Button>
        </Link>
      </Show>
    </div>
  );
};

export default VoteRefund;
