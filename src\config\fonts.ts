import { Inter as FontSans, Open_Sans } from 'next/font/google';
import localFont from 'next/font/local';

export const openSans = Open_Sans({
  subsets: ['latin'],
  variable: '--font-open-sans',
  display: 'swap',
});

export const fontSans = FontSans({
  subsets: ['latin'],
  variable: '--font-sans',
  weight: ['400', '500', '600', '700'],
});

// export const fontOrbitron = Orbitron({
//   subsets: ['latin'],
//   variable: '--font-orbitron',
//   weight: ['900', '400', '500', '600', '700', '800'],
//   display: 'swap',
// });

export const fontOrbitron = localFont({
  src: [
    {
      path: '../assets/fonts/orbitron/Orbitron-Regular.ttf',
      weight: '400',
    },
    {
      path: '../assets/fonts/orbitron/Orbitron-Medium.ttf',
      weight: '500',
    },
    {
      path: '../assets/fonts/orbitron/Orbitron-SemiBold.ttf',
      weight: '600',
    },
    {
      path: '../assets/fonts/orbitron/Orbitron-Bold.ttf',
      weight: '700',
    },
    {
      path: '../assets/fonts/orbitron/Orbitron-ExtraBold.ttf',
      weight: '800',
    },
    {
      path: '../assets/fonts/orbitron/Orbitron-Black.ttf',
      weight: '900',
    },
  ],
  variable: '--font-orbitron',
  style: 'normal',
  display: 'swap',
});

// export const fontGeistMono = Geist_Mono({
//   subsets: ['latin'],
//   variable: '--font-geistMono',
//   weight: ['900', '400', '500', '600', '700', '800'],
//   display: 'swap',
// });
export const fontGeistMono = localFont({
  src: [
    {
      path: '../assets/fonts/geist_mono/GeistMono-Thin.ttf',
      weight: '100',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-ExtraLight.ttf',
      weight: '200',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-Light.ttf',
      weight: '300',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-Regular.ttf',
      weight: '400',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-Medium.ttf',
      weight: '500',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-SemiBold.ttf',
      weight: '600',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-Bold.ttf',
      weight: '700',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-ExtraBold.ttf',
      weight: '800',
    },
    {
      path: '../assets/fonts/geist_mono/GeistMono-Black.ttf',
      weight: '900',
    },
  ],
  variable: '--font-geistMono',
  style: 'normal',
  display: 'swap',
});

// export const fontGeist = Geist({
//   subsets: ['latin'],
//   variable: '--font-geist',
//   weight: ['900', '400', '500', '600', '700', '800'],
//   display: 'swap',
// });

export const fontGeist = localFont({
  src: [
    {
      path: '../assets/fonts/geist/Geist-Thin.ttf',
      weight: '100',
    },
    {
      path: '../assets/fonts/geist/Geist-ExtraLight.ttf',
      weight: '200',
    },
    {
      path: '../assets/fonts/geist/Geist-Light.ttf',
      weight: '300',
    },
    {
      path: '../assets/fonts/geist/Geist-Regular.ttf',
      weight: '400',
    },
    {
      path: '../assets/fonts/geist/Geist-Medium.ttf',
      weight: '500',
    },
    {
      path: '../assets/fonts/geist/Geist-SemiBold.ttf',
      weight: '600',
    },
    {
      path: '../assets/fonts/geist/Geist-Bold.ttf',
      weight: '700',
    },
    {
      path: '../assets/fonts/geist/Geist-ExtraBold.ttf',
      weight: '800',
    },
    {
      path: '../assets/fonts/geist/Geist-Black.ttf',
      weight: '900',
    },
  ],
  variable: '--font-geist',
  style: 'normal',
  display: 'swap',
});

// export const fontBangers = Bangers({
//   subsets: ['latin'],
//   variable: '--font-bangers',
//   weight: ['400'],
//   display: 'swap',
// });

export const fontBangers = localFont({
  src: [
    {
      path: '../assets/fonts/bangers/Bangers-Regular.ttf',
      weight: '400',
    },
  ],
  variable: '--font-bangers',
  style: 'normal',
  display: 'swap',
});

// export const fontJersey15 = Jersey_15({
//   subsets: ['latin'],
//   variable: '--font-jersey15',
//   weight: '400',
//   display: 'swap',
// });

export const fontJersey15 = localFont({
  src: [
    {
      path: '../assets/fonts/jersey_15/Jersey15-Regular.ttf',
      weight: '400',
    },
  ],
  variable: '--font-jersey15',
  style: 'normal',
  display: 'swap',
});

export const fontMetropolis = localFont({
  src: [
    {
      path: '../assets/fonts/metropolis/Metropolis-Thin.otf',
      weight: '100',
    },
    {
      path: '../assets/fonts/metropolis/Metropolis-Regular.otf',
      weight: '400',
    },
    {
      path: '../assets/fonts/metropolis/Metropolis-Medium.otf',
      weight: '500',
    },
    {
      path: '../assets/fonts/metropolis/Metropolis-Bold.otf',
      weight: '700',
    },
  ],
  variable: '--font-metropolis',
  style: 'normal',
  display: 'swap',
});
