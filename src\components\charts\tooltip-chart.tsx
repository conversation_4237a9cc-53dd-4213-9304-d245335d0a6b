import React from 'react';

import type { FCC } from '@/types';
import { VStack } from '../utilities';

const TooltipChart: FCC<any> = ({ active, payload, label, name }) => {
  if (active && payload && payload.length) {
    return (
      <VStack className="rounded-sm border border-gray-300 bg-white px-4 py-2.5 shadow-sm">
        <p className="font-semibold text-gray-500 text-sm">Date: {label} </p>
        <p className="mt-1 text-primary-500 text-sm">
          Prize Pool: <span className="font-semibold">{Number(payload[0].payload.total_bet_amount.toFixed(5)).toString() || 0}</span>
        </p>
        <p className="mt-1 text-primary-500 text-sm">
          {name}: <span className="font-semibold">{Number(payload[0].payload.agent_bet.toFixed(5)).toString() || 0}</span>
        </p>
        <p className="mt-1 text-primary-500 text-sm">
          Opponent: <span className="font-semibold">{Number(payload[0].payload.opponent_bet.toFixed(5)).toString() || 0}</span>
        </p>
        <p className="mt-1 text-primary-500 text-sm">
          Won:{' '}
          <span className="font-semibold">
            {payload[0].payload?.total_battle_won}/{payload[0].payload?.total_battle}
          </span>
        </p>
      </VStack>
    );
  }

  return <>ss</>;
};

export default TooltipChart;
