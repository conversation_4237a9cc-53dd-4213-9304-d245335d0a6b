import { EEventSocket, socket } from '@/config/socket';
import { getCookie } from 'cookies-next';
import { useEffect } from 'react';
import { type QueryHookOptions, createQuery } from 'react-query-kit';
import {
  getAllBattleForGenerateStatic,
  getBattleById,
  getBattleForSaleById,
  getBattleInfoById,
  getBattleReportById,
  getBattlesActivity,
  getCurrentUserClaim,
  getJudgeComment,
  getListBattles,
  getListLiveBattle,
  getTopBettors,
  getUserBetting,
  getYourBets,
} from './requests';
import type {
  IBattleDetail,
  IBattleReport,
  ICurrentUserClaim,
  IJudgeComment,
  ILiveBattleQuery,
  ILiveBattleResponse,
  IParamsBattle,
  IParamsBattleActivity,
  IResponseBattleDashboard,
  IResponsiveBattleActivity,
  IResponsiveTopBattors,
  IUserBettingResponse,
  IYourBet,
} from './types';

export const useTopBettorsQuery = createQuery<IResponsiveTopBattors, { battle_id: string; agent_id?: string }>({
  queryKey: ['/battles/top-battors'],
  fetcher: (params) => getTopBettors(params),
});

export const useTopBettors = (
  options?: QueryHookOptions<IResponsiveTopBattors, Error, IResponsiveTopBattors, { battle_id: string; agent_id?: string }> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, isFetching, ...rest } = useTopBettorsQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables || isFetching) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    };
  }, [options?.variables, isFetching]);

  return { refetch, isFetching, ...rest };
};

export const useBattleActivityQuery = createQuery<IResponsiveBattleActivity, IParamsBattleActivity>({
  queryKey: ['/battles/activity'],
  fetcher: (params) => getBattlesActivity(params),
});

export const useBattleActivity = (
  options?: QueryHookOptions<IResponsiveBattleActivity, Error, IResponsiveBattleActivity, IParamsBattleActivity> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, isFetching, ...rest } = useBattleActivityQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables || isFetching) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    };
  }, [options?.variables, isFetching]);

  return { refetch, isFetching, ...rest };
};

export const useBattleByIdQuery = createQuery<IBattleDetail, string>({
  queryKey: ['/battles/detail'],
  fetcher: (params) => getBattleById(params),
});

export const useBattleForSaleByIdQuery = createQuery<IBattleDetail, string>({
  queryKey: ['/battles/for-sale'],
  fetcher: (params) => getBattleForSaleById(params),
});

export const useBattleById = (
  options?: QueryHookOptions<IBattleDetail, Error, IBattleDetail, string> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, ...rest } = useBattleByIdQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.DEBATE_RESOLVED}_${options?.variables}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.DEBATE_RESOLVED}_${options?.variables}`, handleMessage);
    };
  }, [options?.variables]);

  return { refetch, ...rest };
};

export const useBattleReportByIdQuery = createQuery<IBattleReport, string>({
  queryKey: ['/battles/report'],
  fetcher: (params) => getBattleReportById(params),
});

export const useBattleReportById = (
  options?: QueryHookOptions<IBattleReport, Error, IBattleReport, string> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, isFetching, ...rest } = useBattleReportByIdQuery(options, queryClient);

  useEffect(() => {
    if ((!options?.variables && options?.enabled) || isFetching) return;
    const handleMessage = () => {
      refetch();
    };

    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    };
  }, [options?.variables, isFetching]);

  return { refetch, isFetching, ...rest };
};

export const useBattleInfoByIdQuery = createQuery<IBattleReport, string>({
  queryKey: ['/battles/info'],
  fetcher: (params) => getBattleInfoById(params),
});

export const useBattleInfoById = (
  options?: QueryHookOptions<IBattleReport, Error, IBattleReport, string> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, isFetching, ...rest } = useBattleInfoByIdQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables || isFetching) return;

    const handleMessage = () => {
      refetch();
    };

    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    };
  }, [options?.variables, isFetching]);

  return { refetch, isFetching, ...rest };
};

export const useListBattle = createQuery<IResponseBattleDashboard, IParamsBattle>({
  queryKey: ['/battles/list-battle'],
  fetcher: (params) => getListBattles(params),
});

export const useListLiveBattle = createQuery<ILiveBattleResponse, ILiveBattleQuery>({
  queryKey: ['/api/live-battle'],
  fetcher: (params) => getListLiveBattle(params),
});

export const useCurrentUserClaim = createQuery<ICurrentUserClaim, string>({
  queryKey: ['/battle/current-user-claim'],
  fetcher: (params) => getCurrentUserClaim(params),
});

export const useUserBettingQuery = createQuery<IUserBettingResponse, { params?: any; battle_id: string }>({
  queryKey: ['/battle/user-betting/'],
  fetcher: (params) => getUserBetting(params),
});

export const useUserBetting = (
  options?: QueryHookOptions<IUserBettingResponse, Error, IUserBettingResponse, { params?: any; battle_id: string }> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, isFetching, ...rest } = useUserBettingQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables?.battle_id || isFetching) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables?.battle_id}`, handleMessage);
    };
  }, [options?.variables?.battle_id, isFetching]);

  return { refetch, isFetching, ...rest };
};

export const useJudgeCommentQuery = createQuery<IJudgeComment[], string>({
  queryKey: ['/api/battle/get-judge-comment/'],
  fetcher: (params) => getJudgeComment(params),
});

export const useJudgeComment = (
  options?: QueryHookOptions<IJudgeComment[], Error, IJudgeComment[], string> | undefined,
  queryClient?: void | undefined
) => {
  const { refetch, ...rest } = useJudgeCommentQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.DEBATE_RESOLVED}_${options?.variables}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.DEBATE_RESOLVED}_${options?.variables}`, handleMessage);
    };
  }, [options?.variables]);

  return { refetch, ...rest };
};

export const useYourBetsQuery = createQuery<IYourBet[], string>({
  queryKey: ['/battle/your-bets/'],
  fetcher: (params) => getYourBets(params),
});

export const useYourBets = (
  options?: QueryHookOptions<IYourBet[], Error, IYourBet[], string> | undefined,
  queryClient?: void | undefined
) => {
  const accessToken = getCookie('access_token');
  const { refetch, isFetching, ...rest } = useYourBetsQuery(options, queryClient);

  useEffect(() => {
    if (!options?.variables || !accessToken || isFetching) return;
    const handleMessage = () => {
      refetch();
    };
    socket.on(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    return () => {
      socket.off(`${EEventSocket.BET_PLACED}_${options?.variables}`, handleMessage);
    };
  }, [options?.variables, isFetching, accessToken]);

  return { refetch, isFetching, ...rest };
};

export const useAllBattleForGenerateStatic = createQuery<IBattleDetail[], void>({
  queryKey: ['/api/battles/all-for-fe-generate-static'],
  fetcher: () => getAllBattleForGenerateStatic(),
});
