export const getBase64FromUrl = async (url: string) => {
  const img = new Image();
  img.crossOrigin = 'anonymous';
  img.src = `${url}?__v=${Date.now()}`;
  return new Promise((resolve) => {
    img.onload = function () {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      ctx.drawImage(img, 0, 0);
      const base64String = canvas.toDataURL('image/png');
      resolve(base64String);
    };
  });
};
