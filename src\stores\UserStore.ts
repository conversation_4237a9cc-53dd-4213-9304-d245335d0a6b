import type { IUser } from '@/api/auth/types';
import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { create } from 'zustand';

export interface IUserStore {
  user: IUser;
  isVerifiedUser: boolean | undefined;
  verifyToken: string;

  setUser: (data: IUser) => void;
  setIsVerifiedUser: (data: boolean | undefined) => void;
  setVerifyToken: (data: string) => void;
  logout: () => void;
}

const useBaseUserStore = create<IUserStore>()(
  // persist(
  (set) => ({
    verifyToken: '',
    isVerifiedUser: undefined,
    user: {} as IUser,
    setUser: (data) => set((state) => ({ ...state, user: data })),
    setVerifyToken: (data) => set((state) => ({ ...state, verifyToken: data })),
    setIsVerifiedUser: (data) => set((state) => ({ ...state, isVerifiedUser: data })),
    logout: () => set(() => ({ accessToken: '', verifyToken: '', user: {} as IUser })),
  })
  // {
  //   name: 'user-store',
  //   storage: createJSONStorage(() => localStorage),
  // })
);

export const useUserStore = createSelectorFunctions(useBaseUserStore);
