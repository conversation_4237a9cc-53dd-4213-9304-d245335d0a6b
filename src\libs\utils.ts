import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatNumber = (number?: number, fractionDigits: number = 5): string => {
  if (number === 0) return '0';
  if (number === null || typeof number !== 'number' || isNaN(number)) return '0';

  if (Number.isInteger(number)) {
    return number.toLocaleString('en-US');
  }

  let roundedNumber = Number(number.toFixed(fractionDigits));

  return roundedNumber.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: fractionDigits,
  });
};

export const convertNumberToShortForm = (number?: number): string => {
  if (!number) return '0';
  const units = ['', 'K', 'M', 'B', 'T', 'P', 'E'];
  const tier = Math.floor(Math.log10(number) / 3);

  if (tier === 0) return number.toString();

  const suffix = units[tier];
  const scale = Math.pow(10, tier * 3);
  const scaledNumber = number / scale;

  return `${scaledNumber.toFixed(1)}${suffix}`;
};

export function truncate(str: string, length: number) {
  return str.length > length ? `${str.substring(0, length)}...` : str;
}

export function shortenAddress(str?: string, length = 4) {
  if (!str) return '';
  if (str?.length < length) return str;
  return `${str.substring(0, 6)}...${str.substring(str.length - length)}`;
}
