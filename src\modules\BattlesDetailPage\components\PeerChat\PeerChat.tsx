import { useBattleById } from '@/api/battles/queries';
import { getListComments } from '@/api/comment/requests';
import type { ICommentQuery } from '@/api/comment/types';
import { Icons } from '@/assets/icons';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HStack, Show, VStack } from '@/components/utilities';
import { SOCKET_LISTENER_EVENT, socket } from '@/config/socket';
import { useUserLogin } from '@/hooks/useUserLogin';
import { cn, onMutateError } from '@/libs/common';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { data_filter_peer_chat } from '../../utils/const';
import RightChat from './components/RightChat';
import SendNewChat from './components/SendNewChat';

const PeerChat = () => {
  const [valueFilterPeetChat, setValueFilterPeetChat] = useState<string>(data_filter_peer_chat?.[0]?.value);
  const [paramsQuery, setParamsQuery] = useState<Partial<ICommentQuery>>({
    limit: 20,
    page: 1,
    sort_type: 'DESC',
  });
  const params = useParams();
  const id = params?.id;
  const { data: battle } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });

  const queryClient = useQueryClient();
  const [expandedParentId, setExpandedParentId] = useState<string | null>(null);
  const { ref, inView } = useInView({ rootMargin: '0px 0px 0px 0px', root: null, threshold: 0 });
  const { isLoggedIn } = useUserLogin();
  const {
    data: listComment,
    refetch,
    isFetching,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ['/api/comments', paramsQuery, battle?.id],
    refetchOnMount: true,
    queryFn: ({ pageParam = 1 }) =>
      getListComments({ ...paramsQuery, page: pageParam, battle_id: String(battle?.id), sort_key: valueFilterPeetChat }),
    getNextPageParam: (lastPage, pages) => {
      if (Number(lastPage?.pagination?.page) >= Number(lastPage?.pagination?.total_page)) return undefined;
      return Number(lastPage?.pagination?.page) + 1;
    },
    enabled: Boolean(id && battle?.id),
    onError: onMutateError,
  });

  useEffect(() => {
    if (valueFilterPeetChat) {
      setParamsQuery((prev) => ({ ...prev, sort_key: valueFilterPeetChat }));
    }
  }, [valueFilterPeetChat]);

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  useEffect(() => {
    if (!id) return;

    const handleComment = (cmt: any) => {
      if (!cmt) return;
      refetch();
      queryClient.refetchQueries(['/api/child-comments', { parent_id: cmt.parent }]);
    };
    socket.on(`${SOCKET_LISTENER_EVENT.COMMENT}_${id}`, handleComment);
    // eslint-disable-next-line consistent-return
    return () => {
      socket.off(`${SOCKET_LISTENER_EVENT.COMMENT}_${id}`, handleComment);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [socket, id]);

  return (
    <div className="flex flex-col justify-between md:h-[calc(100%-68px)]">
      <VStack spacing={12} className="max-h-screen flex-1 overflow-auto pt-4 lg:pt-6">
        <HStack spacing={16} noWrap className="md:px-4 lg:px-6">
          <Select
            key={data_filter_peer_chat?.[0]?.value}
            value={valueFilterPeetChat}
            onValueChange={(value) => setValueFilterPeetChat(value)}
          >
            <SelectTrigger
              icon={<Icons.caretDown size={16} />}
              className={cn(
                'flex w-fit items-center gap-2 border-none px-0 font-geistMono font-medium text-black text-sm uppercase shadow-none data-[state=open]:ring-0'
              )}
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white font-geistMono">
              {data_filter_peer_chat?.map((item: { value: string; label: string }, index: number) => (
                <SelectItem value={item.value} key={index} className="text-xs">
                  {item.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </HStack>

        <div className={cn('relative grid w-full grid-cols-1 gap-6')}>
          <RightChat
            listComment={listComment}
            refetch={refetch}
            ref={ref}
            isFetching={isFetching}
            expandedParentId={expandedParentId}
            setExpandedParentId={setExpandedParentId}
          />
        </div>
      </VStack>
      <Show when={isLoggedIn}>
        <SendNewChat refetch={refetch} />
      </Show>
    </div>
  );
};

export default PeerChat;
