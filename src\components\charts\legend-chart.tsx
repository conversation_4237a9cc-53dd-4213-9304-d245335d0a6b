import React from 'react';
import { HStack } from '../utilities';

const LegendChart = ({ name }: { name?: string }) => {
  return (
    <HStack spacing={12} className="items-center justify-end text-xs">
      <HStack spacing={4} className="font-semibold text-tertiary-900">
        <div className="h-3 w-4 rounded-sm bg-[#E06D38]" />
        {name}
      </HStack>
      <HStack spacing={4} className="font-semibold text-tertiary-900">
        <div className="h-3 w-4 rounded-sm bg-[#EDB084]" />
        Opponent
      </HStack>
    </HStack>
  );
};

export default LegendChart;
