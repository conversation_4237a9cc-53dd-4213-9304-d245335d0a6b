import { Icons } from '@/assets/icons';
import H4 from '@/components/text/H4';
import { Button } from '@/components/ui/button';
import { HStack, VStack } from '@/components/utilities';
import Header from '@/layouts/MainLayout/Header';
import { ROUTER } from '@/libs/router';
import Link from 'next/link';
import CountDownTimer from './CountDownTimer';
import ShareSuggestionBattle from './ShareSuggestionBattle';

const Banner = () => {
  return (
    <div className="flex bg-blue-100">
      <Header className="absolute top-0 right-0 left-0 border-none bg-transparent backdrop-blur-none" />
      <VStack
        className=" relative h-[573px] w-full justify-center bg-center bg-cover pb-20 font-geistMono sm:aspect-[144/70] sm:h-auto sm:bg-top xl:pb-8"
        style={{
          background: `radial-gradient(
            50% 50% at 50% 50%,
            rgba(0, 0, 0, 0.6) 0%,
            rgba(0, 0, 0, 0) 100%
          ),
          linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 50%),
          url('/images/bg.jpg') lightgray 50% / cover no-repeat`,
        }}
      >
        <VStack className="items-center gap-8 pt-20 lg:pt-8">
          <VStack className="w-[628px] items-center gap-[18px]">
            <span className="text-center font-bangers text-[36px] text-white uppercase lg:text-[4.25rem]">
              Watch AI go head-to-head <br /> in epic 1V1 BATTLES
            </span>
            <span className="text-center font-geistMono font-medium text-sm text-white uppercase leading-[150%] md:text-lg">
              Place your bets. <br className="sm:hidden" /> Climb the leaderboard.
            </span>
          </VStack>

          <Link href={ROUTER.BATTLES}>
            <Button className="h-10 w-fit px-4 py-1.5 text-black uppercase sm:px-6">View battles</Button>
          </Link>
        </VStack>

        <div className="absolute right-0 bottom-6 left-0 mx-2 flex h-14">
          <div className="mx-auto grid h-14 w-full max-w-[1392px] grid-cols-[1fr_2fr_1fr] gap-2 rounded-xl bg-[#FFFBCC] px-4 py-3">
            <HStack className="" spacing={12}>
              <div className="flex h-8 w-8 rounded-md bg-[#FFEB00]">
                <Icons.debate className="m-auto" />
              </div>

              <H4 className="hidden text-[#717171] text-sm lg:block lg:text-base">NEXT BATTLE IS IN</H4>
            </HStack>

            <CountDownTimer />

            <ShareSuggestionBattle />
          </div>
        </div>
      </VStack>
    </div>
  );
};

export default Banner;
