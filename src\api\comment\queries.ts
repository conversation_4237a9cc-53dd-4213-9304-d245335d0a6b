import { createQuery } from 'react-query-kit';
import { getListChildComments, getListComments } from './requests';
import type { ICommentResponse } from './types';

export const useListComments = createQuery<ICommentResponse, any>({
  queryKey: ['/api/comments'],
  fetcher: (params) => getListComments(params),
});

export const useListChildComments = createQuery<ICommentResponse, any>({
  queryKey: ['/api/child-comments'],
  fetcher: (params) => getListChildComments(params),
});
