## Intro <a name="intro"></a>

This boilerplate is made for creating NextJS application easier and faster.

## Getting started: <a name="getting-started"></a>

1. When you're using Windows run this:

   - `git config --global core.eol lf`
   - `git config --global core.autocrlf input`

   #### This will change eol(End of line) to the same as on Linux/Mac, without this, you will have conflicts with your teammates with those systems and our bash script won't work

2. Clone this repository.
3. Install pnpm globally: `npm install -g pnpm` (check your node version >= 18)
4. Run `pnpm install`

---

### Run the development server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

---

Made by [HoangDevNull](https://github.com/hoangdevnull)
