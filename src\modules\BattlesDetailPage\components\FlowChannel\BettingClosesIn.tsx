'use client';

import { useBattleById } from '@/api/battles/queries';
import { HStack } from '@/components/utilities';
import useCheckTimeOverBattle from '@/hooks/useCheckTimeOverBattle';
import useCountdownTimer from '@/hooks/useCountdownTimer';
import { cn, onMutateError } from '@/libs/common';
import { useParams } from 'next/navigation';
import React, { useLayoutEffect } from 'react';

const BettingClosesIn = () => {
  const { id } = useParams();

  const { data, isFetching } = useBattleById({ variables: String(id), enabled: !!id, onError: onMutateError });
  const timer = data?.start_at
    ? new Date(new Date(data?.start_at).getTime() + (Number(data?.duration) <= 5 ? 0 : Number(data?.duration) - 5) * 60 * 1000)
    : undefined;
  const { start, seconds, days, hours, minutes } = useCountdownTimer();
  const { isBattleStillBet } = useCheckTimeOverBattle({
    duration: data?.duration,
    start_at: data?.start_at,
  });

  useLayoutEffect(() => {
    if (timer) {
      start(timer);
    }
  }, [timer]);

  if (!isBattleStillBet || isFetching || (!days && !hours && !minutes && !seconds)) return null;

  return (
    <HStack
      pos="apart"
      className={cn('my-6 font-medium text-dark-100 text-sm uppercase', {
        'text-[#FF3901]': days < 1 && hours < 1 && minutes < 6,
      })}
    >
      <div className="">Betting closes in</div>

      <div className="space-x-1">
        {days > 0 && <span>{days}D</span>}
        {hours > 0 && <span>{hours}H</span>}
        {minutes > 0 && <span>{minutes}M</span>}
        <span>{seconds}S</span>
      </div>
    </HStack>
  );
};

export default BettingClosesIn;
