import React, { useEffect, useState } from 'react';

type Props = {
  start_at?: Date | string;
  duration?: number;
  betEndOffset?: number;
};

const useCheckTimeOverBattle = ({ duration, start_at, betEndOffset = 5 }: Props) => {
  const [isBattleStillBet, setIsBattleStillBet] = useState<boolean>(true);
  const [isBattleStillActive, setIsBattleStillActive] = useState<boolean>(true);

  useEffect(() => {
    if (!start_at || !duration) return;
    const startTime = new Date(start_at).getTime();
    if (isNaN(startTime)) {
      return;
    }

    const endTime = startTime + duration * 60 * 1000;
    const betEndTime = endTime - betEndOffset * 60 * 1000;

    const checkTime = () => {
      const now = Date.now();

      setIsBattleStillBet(now < betEndTime);
      setIsBattleStillActive(now < endTime);
    };

    checkTime();

    const id = setInterval(checkTime, 1000);

    return () => clearInterval(id);
  }, [start_at, duration, betEndOffset]);

  return { isBattleStillBet, isBattleStillActive };
};

export default useCheckTimeOverBattle;
