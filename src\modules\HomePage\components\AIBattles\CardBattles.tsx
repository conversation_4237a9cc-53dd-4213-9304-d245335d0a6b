import type { IDataBattleDashboard } from '@/api/battles/types';
import BattleStatusDisplay from '@/components/BattleStatusDisplay';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import moment from 'moment';
import { useRouter } from 'next-nprogress-bar';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { getStatus } from './utils/status-custom';

const CardBattles = ({ item, loading }: { item?: IDataBattleDashboard; loading?: boolean }) => {
  const [remainingTime, setRemainingTime] = useState('');
  const [minutes, setMinutes] = useState('00');
  const [seconds, setSeconds] = useState('00');
  const router = useRouter();
  const [isHover, setIsHover] = useState(false);

  useEffect(() => {
    if (item?.status !== 'ongoing') return;

    const interval = setInterval(() => {
      const now = new Date();
      const startTime = new Date(item.start_at);
      const endTime = new Date(startTime.getTime() + item.duration * 60000);
      const diff = Math.max(0, (endTime.getTime() - now.getTime()) / 1000);

      const minutes = Math.floor(diff / 60);
      const seconds = Math.floor(diff % 60);
      setMinutes(String(minutes).padStart(2, '0'));
      setSeconds(String(seconds).padStart(2, '0'));
      const formattedTime = `${minutes}m${seconds}s`;

      setRemainingTime(formattedTime === '0m0s' ? 'Choosing Winner...' : formattedTime);

      if (diff <= 0) clearInterval(interval);
    }, 1000);

    return () => clearInterval(interval);
  }, [item]);

  const getNameWinner = () => {
    if (!item?.winner_id) {
      return '';
    }
    if (item?.winner_id === item?.agent1.id) {
      return item?.agent1.name;
    }

    if (item?.winner_id === item?.agent2.id) {
      return item?.agent2.name;
    }
  };

  return (
    <VStack
      spacing={20}
      onMouseMove={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      className="group relative z-1 h-full cursor-pointer justify-between gap-8 self-stretch rounded-xl p-8"
      onClick={() => router.push(`/battles/${item?.id}/${item?.slug}`)}
    >
      <VStack className="w-full flex-1 items-center justify-between gap-6">
        <HStack
          pos={'apart'}
          noWrap
          className="-space-x-7 relative w-full items-center justify-center self-stretch transition-all duration-200 group-hover:scale-[1.18]"
        >
          <SkeletonWrapper loading={loading}>
            <div
              className="-rotate-[7deg] rounded-2xl "
              style={{
                boxShadow:
                  !item?.winner || item?.agent1?.id === item?.winner?.id
                    ? !isHover
                      ? `-154.88px 197.281px 70.063px 0px ${item?.agent1?.background}00, -99.56px 125.375px 64.531px 0px ${item?.agent1?.background}05, -23.97px 31.344px 40.563px 0px ${item?.agent1?.background}2E`
                      : `-4.216px 5.623px 16.87px 0px ${item?.agent1?.background}33, -18.276px 23.899px 30.928px 0px ${item?.agent1?.background}2E, -9.77px 53.421px 40.768px 0px ${item?.agent1?.background}1A, -75.912px 95.595px 49.203px 0px ${item?.agent1?.background}05, -118.092px 150.421px 53.421px 0px ${item?.agent1?.background}00`
                    : undefined,
              }}
            >
              <Image
                src={item?.agent1?.image_url || '/images/no-image.png'}
                width={136}
                height={136}
                alt="agent1"
                className={cn('h-[136px] w-[136px] origin-right rounded-2xl object-cover', {
                  'opacity-25 brightness-150 grayscale': item?.winner_id && item?.winner_id === item?.agent2.id && !!item?.winner?.id,
                })}
              />
            </div>
          </SkeletonWrapper>

          <SkeletonWrapper loading={loading}>
            <div
              className="rotate-[7deg] rounded-2xl "
              style={{
                boxShadow:
                  !item?.winner || item?.agent2?.id === item?.winner?.id
                    ? !isHover
                      ? `84px 107px 38px 0px ${item?.agent2?.background}05, 54px 68px 35px 0px ${item?.agent2?.background}14, 30px 38px 29px 0px ${item?.agent2?.background}1A, 13px 17px 22px 0px ${item?.agent2?.background}00, 3px 4px 12px 0px ${item?.agent2?.background}00`
                      : `2.582px 3.443px 10.329px 0px ${item?.agent2?.background}33, 11.19px 14.633px 18.937px 0px ${item?.agent2?.background}2E, 25.823px 32.709px 24.962px 0px ${item?.agent2?.background}1A, 20.658px 58.532px 30.127px 0px ${item?.agent2?.background}14, 72.304px 92.101px 32.709px 0px ${item?.agent2?.background}05`
                    : undefined,
              }}
            >
              <Image
                src={item?.agent2?.image_url || '/images/no-image.png'}
                width={136}
                height={136}
                alt="agent2"
                className={cn('h-[136px] w-[136px] origin-left rounded-2xl object-cover transition-all duration-200 ', {
                  'opacity-25 brightness-150 grayscale': item?.winner_id && item?.winner_id === item?.agent1.id && !!item?.winner?.id,
                })}
              />
            </div>
          </SkeletonWrapper>

          <div className="absolute top-[30%] left-1/2 h-[45px] w-[50px] translate-x-[5.5%] rotate-[5deg] ">
            <Image
              src="/images/vs.png"
              alt="logo"
              height={50}
              width={62}
              className="transition-all duration-200 group-hover:translate-y-[8px] group-hover:scale-[1.2]"
            />
          </div>
        </HStack>

        <VStack className="flex-1 gap-2">
          <SkeletonWrapper loading={loading}>
            <HStack spacing={12} className="flex-nowrap justify-center text-center font-jersey15 text-[2rem] uppercase">
              <span className="break-word line-clamp-1"> {item?.agent1?.name}</span>
              <span className="font-bold font-openSans text-[#131313] text-sm leading-[140%] tracking-[-0.14px]">
                {item?.agent1?.name && item?.agent2?.name ? 'x' : null}
              </span>
              <span className="break-word line-clamp-1">{item?.agent2?.name}</span>
            </HStack>
          </SkeletonWrapper>
          <p className="line-clamp-3 text-center font-geistMono font-medium text-[#717171] text-sm uppercase leading-[150%] tracking-[-0.14px]">
            {item?.topic}
          </p>
        </VStack>

        <SkeletonWrapper loading={loading}>{getStatus(String(item?.status), getNameWinner())}</SkeletonWrapper>
      </VStack>

      <HStack pos={'apart'} className="w-full py-2 font-geistMono">
        <VStack>
          <SkeletonWrapper loading={loading}>
            <p className="font-medium text-[#717171] text-sm uppercase">Start Time</p>
          </SkeletonWrapper>
          <SkeletonWrapper loading={loading}>
            <p className="font-semibold text-[#131313] text-sm">{moment(item?.start_at).format('MM/DD/YYYY - hh:mm A')}</p>
          </SkeletonWrapper>
        </VStack>
        <VStack spacing={8}>
          <BattleStatusDisplay
            status={item?.status}
            remainingTime={remainingTime}
            duration={item?.duration}
            minutes={minutes}
            seconds={seconds}
          />
        </VStack>
      </HStack>
    </VStack>
  );
};

export default CardBattles;
