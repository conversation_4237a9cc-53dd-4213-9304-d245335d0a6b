import React from 'react';

import { Icons } from '@/assets/icons';
import { Show, VStack } from '../utilities';

type Props = {
  isFetching: boolean;
  data: any;
  content?: string;
};
const Empty = ({ isFetching, data, content = 'No data' }: Props) => {
  return (
    <Show when={isFetching || !data || data?.length === 0}>
      <div className="my-auto flex items-center justify-center">
        {isFetching && <Icons.loading className="animate-spin" />}

        <Show when={!isFetching && (!data || data.length === 0)}>
          <VStack align="center">
            <Icons.noDataTable />
            {/* <p className="font-semibold text-gray-600 text-xs">{content}</p> */}
          </VStack>
        </Show>
      </div>
    </Show>
  );
};

export default Empty;
