'use client';
import { useParentAgent } from '@/api/agent/queries';
import { HStack, Show, VStack } from '@/components/utilities';
import { onMutateError } from '@/libs/common';
import DebaterAgentItem from '@/modules/AgentsPage/components/DebaterAgentItem';

const GenZero = () => {
  const { data, isFetching } = useParentAgent({ onError: onMutateError });

  return (
    <VStack className="gap-12">
      <HStack className="justify-between">
        <div>
          <p className="align-middle font-bangers font-normal text-5xl leading-[140%] tracking-[0%]">Gen o</p>
          <p className="align-middle font-geistMono font-medium text-[#717171] text-base uppercase leading-[150%] tracking-[0%]">
            First Generation: Pure, Unaltered Intelligence
          </p>
        </div>
        {/* to do */}
        {/* <div className="self-end">
          <Select key={date_filter?.[0]?.value} value={valueDate} onValueChange={handleDateRangeChange}>
            <SelectTrigger icon={<ChevronDown size={16} />}>
              <span className="text-center align-middle font-medium text-[#717171] text-sm uppercase leading-[150%] tracking-[0%]">
                Sort By
              </span>
              <span className="text-[#000000]">
                <SelectValue />
              </span>
            </SelectTrigger>
            <SelectContent className="bg-white">
              {date_filter?.map((item: any, index) => (
                <SelectItem
                  value={item.value}
                  key={index}
                  className="h-9 cursor-pointer font-medium text-[#000000] text-sm uppercase hover:text-[#FF3901] focus:bg-[#FFEB00] focus:text-[#FF3901]"
                >
                  {item.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div> */}
      </HStack>

      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        <Show when={isFetching}>
          {Array.from({ length: Number(6) }).map((_, index) => (
            <DebaterAgentItem loading={isFetching} key={index} />
          ))}
        </Show>

        {data?.map((item) => (
          <DebaterAgentItem key={item.id} {...item} />
        ))}
      </div>
    </VStack>
  );
};

export default GenZero;
