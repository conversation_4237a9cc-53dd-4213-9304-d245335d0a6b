'use client';
import type { MIME_TYPE } from '@/libs/mime';
import React, { useRef } from 'react';
import type { Control, FieldPath, FieldPathValue, FieldValues } from 'react-hook-form';

import { uploadAvatar } from '@/api/auth/requests';
import { onMutateError } from '@/libs/common';
import { cn } from '@/libs/utils';
import { useMutation } from '@tanstack/react-query';
import { Loader2, Pencil } from 'lucide-react';
import { Avatar, AvatarImage } from '../ui/avatar';
import type { ButtonProps } from '../ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '../ui/form';

interface Props<T extends FieldValues = FieldValues> extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'accept'> {
  control: Control<T>;
  name: FieldPath<T>;
  defaultValue?: FieldPathValue<T, FieldPath<T>>;
  fullWidth?: boolean;
  readonly?: boolean;
  accept?: MIME_TYPE[];
  loading?: boolean;
  btnProps?: ButtonProps;
}

const UploadAvatarField = <T extends FieldValues>({
  accept = [],
  control,
  name,
  defaultValue,
  btnProps,
  loading,
  className,
  readonly,
  ...props
}: Props<T>) => {
  const ref = useRef<React.ElementRef<'input'>>(null);

  const { mutate, isLoading } = useMutation(uploadAvatar);

  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const handleChangeFile = (e: React.ChangeEvent<HTMLInputElement>, onChange: any) => {
    const formData = new FormData();
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      if (file.size > MAX_FILE_SIZE) {
        onMutateError({ message: 'Avatar size must be less than 5MB.' });
        return;
      }

      formData.append('file', e.target.files[0]);
      formData.append('folder_name', 'avatar');

      mutate(formData, {
        onSuccess: ({ url }) => {
          onChange(url);
        },
        onError: onMutateError,
      });
    }
  };

  return (
    <FormField
      defaultValue={defaultValue}
      control={control}
      name={name}
      render={({ field: { onChange, value } }) => {
        return (
          <FormItem>
            <div
              className={cn('group relative w-fit cursor-pointer', className)}
              onClick={() => {
                if (ref.current) {
                  ref.current.value = '';
                }
                ref.current?.click();
              }}
            >
              <Avatar className="h-28 w-28 rounded-[8px]">
                <AvatarImage src={value || '/avatars/dog.png'} alt="@shadcn" />
              </Avatar>

              {isLoading && (
                <div className="absolute inset-0 m-auto flex bg-[rgba(0,0,0,0.5)]">
                  <Loader2 className="m-auto w-4 animate-spin text-white " />
                </div>
              )}

              {!isLoading && !props.disabled && (
                <div className="absolute inset-0 hidden rounded-full border bg-[rgba(0,0,0,0.5)] transition-all group-hover:flex">
                  <Pencil className="m-auto h-5 w-5 text-white" />
                </div>
              )}
            </div>

            <FormControl>
              <input
                hidden
                accept={accept.length === 0 ? undefined : accept.join(', ')}
                type="file"
                onChange={(e) => {
                  // onChange(Array.from(e.target.files as any));
                  handleChangeFile(e, onChange);
                  e.target.files = null;
                }}
                {...props}
                ref={ref}
              />
            </FormControl>

            <FormMessage className="mt-1 text-xs" />
          </FormItem>
        );
      }}
    />
  );
};

export { UploadAvatarField };
