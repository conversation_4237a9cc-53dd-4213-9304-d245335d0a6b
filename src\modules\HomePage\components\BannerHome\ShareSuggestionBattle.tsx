'use client';
import H1 from '@/components/text/H1';
import { Button } from '@/components/ui/button';
import { Dialog, DialogOverlay, DialogPortal, DialogTrigger } from '@/components/ui/dialog';
import { TextArea } from '@/components/ui/textarea';
import { HStack, Show } from '@/components/utilities';
import { cn } from '@/libs/common';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import React, { useState } from 'react';

const ShareSuggestionBattle = () => {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');

  return (
    <div className="flex items-center justify-end">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button size="sm" className="text-xs md:text-sm lg:text-base">
            Suggest A Battle
          </Button>
        </DialogTrigger>

        <DialogPortal>
          <DialogOverlay className={cn('bg-[#0427FACC]')} />
          <DialogPrimitive.Content
            className={cn(
              'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-[520px] translate-x-[-50%] translate-y-[-50%] gap-4 px-5 py-4 duration-200 data-[state=closed]:animate-out data-[state=open]:animate-in sm:rounded-xl md:max-w-[620px] lg:max-w-[800px]'
            )}
          >
            <H1 className=" text-center font-bangers text-2xl text-white leading-5 sm:text-lg md:text-4xl lg:text-[68px] lg:leading-9">
              Suggest A Battle
            </H1>
            <div className="relative my-6">
              <TextArea placeholder="" value={inputValue} onChange={(e) => setInputValue(e.target.value)} className="border-none" />
              <Show when={!inputValue}>
                <div className="absolute top-2.5 left-3 font-medium text-[#717171] text-xs">
                  Tell us the battle you want to see Engineering <span className="text-[#0427FA] opacity-70">#BOAon0G</span>{' '}
                  <span className="text-[#0427FA] opacity-70">@BattleofAgents</span>
                </div>
              </Show>
            </div>
            <HStack pos="center">
              <Button
                disabled={inputValue?.length < 5}
                onClick={() => {
                  window.open(
                    `http://twitter.com/share?text=${inputValue}&url=${window.location.href}&hashtags=BOAon0G&via=BattleofAgents`
                  );
                }}
              >
                Post On X
              </Button>
            </HStack>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </div>
  );
};

export default ShareSuggestionBattle;
