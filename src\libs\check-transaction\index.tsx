import { useTransactionStore } from '@/stores/useTransactionStore';
import React, { useEffect } from 'react';
import { toast } from 'react-toastify';
import { useWaitForTransactionReceipt } from 'wagmi';
import { customChain } from '../web3/wagmi';

const CheckTransactionProvider = () => {
  const { listDataHash } = useTransactionStore();

  return listDataHash.map((x) => <CheckTransactionItem key={x} hash={x as `0x${string}`} />);
};

export default CheckTransactionProvider;

type CheckTransactionItemProps = {
  hash?: `0x${string}`;
};

const CheckTransactionItem = ({ hash }: CheckTransactionItemProps) => {
  const { isFetching, status } = useWaitForTransactionReceipt({
    chainId: customChain.id,
    hash: hash,
    query: {
      enabled: !!hash,
    },
  });

  useEffect(() => {
    if (!hash) return;
    toast.loading('Waiting for transaction...', { toastId: hash });

    if (status === 'success' && isFetching) {
      toast.update(hash, {
        render: 'Your bet has been placed successfully!',
        type: 'success',
        isLoading: false,
      });
    }

    if (status === 'error' && isFetching) {
      toast.update(hash, {
        render: 'Your bet placement failed. Please try again!',
        type: 'error',
        isLoading: false,
      });
    }
  }, [status, isFetching, hash]);
  return <></>;
};
