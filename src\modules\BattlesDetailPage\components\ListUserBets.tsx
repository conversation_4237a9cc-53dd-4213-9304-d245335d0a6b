'use client';

import { useUserBetting } from '@/api/battles/queries';
import H4 from '@/components/text/H4';
import { Separator } from '@/components/ui/separator';
import { Tooltip } from '@/components/ui/tooltip';
import { HStack, Show } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React from 'react';

const ListUserBets = () => {
  const { id } = useParams();
  const { data, isFetching } = useUserBetting({
    variables: { battle_id: String(id), params: { limit: 4 } },
    enabled: Boolean(id),
    onError: onMutateError,
  });

  if (!data || data?.data?.length < 1) return <></>;

  return (
    <>
      <div className="pb-4 md:px-4 lg:px-6">
        <HStack className="h-full ">
          <div className="-space-x-1 flex">
            {data?.data?.map((x, index) => (
              <div className={cn(index > 0 && 'rounded-sm bg-white pl-0.5')} key={x.id}>
                <Tooltip label={x.username}>
                  <Image src={x.avatar} alt="" width={28} height={28} className="h-7 w-7 rounded object-cover" />
                </Tooltip>
              </div>
            ))}

            <Show when={Number(data?.pagination?.total_item) > 4}>
              <div className="rounded-sm bg-white pl-0.5">
                <div className="flex h-7 items-center rounded-sm bg-[#FFEB00] px-2 font-semibold text-[#FF3901] text-sm">
                  +{Number(data?.pagination?.total_item) - 4}
                </div>
              </div>
            </Show>
          </div>

          <H4 className="ml-1 font-medium text-dark-100 uppercase lg:text-sm">users placed a bet</H4>
        </HStack>
      </div>

      <Separator className="border-[#E5E5E5]" />
    </>
  );
};

export default ListUserBets;
