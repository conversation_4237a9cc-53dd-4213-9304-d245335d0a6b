import { useListBattle } from '@/api/battles/queries';
import NoData from '@/components/NoData';
import HeaderAnonymous from '@/components/no-data/HeaderAnonymous';
import H2 from '@/components/text/H2';
import { HStack, Show } from '@/components/utilities';
import React from 'react';
import CardBattleItem from './CardBattleItem';

const DebateGoLive = () => {
  const { data, isFetching } = useListBattle({
    variables: { page: 1, limit: 2, sort_key: '', sort_type: 'DESC', status: 'ongoing' },
  });

  return (
    <section className="flex flex-col justify-center bg-[#0427FA] py-16">
      <H2 className="text-center font-bangers text-3xl text-[#31FF08] lg:text-5xl">WHICH DEBATE SHOULD GO LIVE?</H2>

      <p className="my-3 text-center font-geistMono font-medium text-[#FFFFFF] text-base uppercase lg:text-lg">
        Place your bet to vote. <br />
        The topic with the most bets wins.
        <br /> You’ll get refunded if your pick doesn’t make it.
      </p>

      <HStack className="mt-4" pos="center" spacing={12}>
        <Show when={isFetching}>
          <CardBattleItem loading={isFetching} />
          <CardBattleItem loading={isFetching} />
        </Show>

        <Show when={!isFetching && data && data?.data?.length > 0}>
          {data?.data?.map((item, index) => (
            <CardBattleItem item={item as any} key={index} />
          ))}
        </Show>

        <Show when={!isFetching && (!data || data?.data?.length === 0)}>
          <div className="rounded-md bg-white px-8 py-6">
            <NoData
              isFetching={isFetching}
              data={data?.data}
              Icon={<HeaderAnonymous />}
              Title={'No Legends Made — Yet'}
              Paragraph="The first epic clash is just around the corner."
              className="mt-8"
              classNames={{
                title: 'text-[#131313] no-shadow',
                paragraph: 'text-[#717171]',
              }}
            />
          </div>
        </Show>
      </HStack>
    </section>
  );
};

export default DebateGoLive;
