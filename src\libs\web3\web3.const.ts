import type { Chain } from 'wagmi/chains';
import { bsc, bscTestnet } from 'wagmi/chains';

import { useTestnet } from '../const';
import { BSC_RPC } from './rpc';

export const BSC_CHAIN = useTestnet ? { ...bscTestnet, rpcUrls: BSC_RPC } : bsc;

export const ALL_EVM_CHAINS = [BSC_CHAIN] as Chain[];

export const MAPPING_WALLET_PROVIDER: Record<string, { src: string; name: string }> = {
  MetaMask: {
    src: '/images/wallet/metamask.svg',
    name: 'MetaMask',
  },
  WalletConnect: {
    src: '/images/wallet/wallet-connect.svg',
    name: 'WalletConnect',
  },
  WalletConnectLegacy: {
    src: '/images/wallet/wallet-connect.svg',
    name: 'WalletConnect',
  },
  'Coinbase Wallet': {
    src: '/images/wallet/coinbase.svg',
    name: 'Coinbase Wallet',
  },
};

export const MAPPING_CHAIN_ICON: Record<number | string, string> = {
  56: '/images/wallet/bsc.svg',
  97: '/images/wallet/bsc.svg',
};

export const MAPPING_CHAIN: Record<number | string, Chain> = {
  56: BSC_CHAIN,
  97: BSC_CHAIN,
};

export const WALLET_SUPPORTS = [
  {
    src: '/images/wallet/metamask.svg',
    name: 'MetaMask',
    title: 'connect METAMASK',
  },
  {
    src: '/images/wallet/wallet-connect.svg',
    name: 'WalletConnect',
    title: 'connect WALLETCONNECT',
  },
];
