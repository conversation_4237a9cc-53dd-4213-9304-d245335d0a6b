import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { create } from 'zustand';

export interface TransactionStore {
  listDataHash: string[];
  setDataHash: (data: string) => void;
}

const useBaseTransactionStore = create<TransactionStore>()((set) => ({
  listDataHash: [],
  setDataHash: (data) => set((state) => ({ ...state, listDataHash: [...state.listDataHash, data] })),
}));

export const useTransactionStore = createSelectorFunctions(useBaseTransactionStore);
