import { useBattleById, useTopBettors } from '@/api/battles/queries';
import { Icons } from '@/assets/icons';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { VStack } from '@/components/utilities';
import { cn, onMutateError } from '@/libs/common';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import TopBettorsTable from './TopBettorsTable';

const TopBettors = () => {
  const params = useParams();
  const [valueAgent, setValueAgent] = useState('ALL');
  const id = params?.id;
  const [paramsQuery, setParamsQuery] = useState<any>();
  const { data: dataAgent } = useBattleById({ variables: String(id), enabled: !!id });
  const { data, isFetching } = useTopBettors({
    variables: { ...paramsQuery, battle_id: String(id), agent_id: valueAgent === 'ALL' ? undefined : String(valueAgent) },
    enabled: Boolean(id),
    refetchOnMount: true,
    keepPreviousData: true,
    onError: onMutateError,
  });
  return (
    <VStack className="gap-0 pt-4 md:gap-3 md:px-4 lg:px-6 lg:pt-6">
      <Select key={dataAgent?.agent1?.id} value={valueAgent} onValueChange={(value) => setValueAgent(value)}>
        <SelectTrigger
          icon={<Icons.caretDown size={16} />}
          className={cn(
            '!border-none flex w-fit items-center gap-2 px-0 font-geistMono font-medium text-black text-sm uppercase shadow-none data-[state=open]:ring-0'
          )}
        >
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-white font-geistMono">
          <SelectItem value={'ALL'} className="text-xs">
            All
          </SelectItem>
          <SelectItem value={dataAgent?.agent1?.id || ''} className="text-xs">
            {dataAgent?.agent1?.name}
          </SelectItem>
          <SelectItem value={dataAgent?.agent2?.id || ''} className="text-xs">
            {dataAgent?.agent2?.name}
          </SelectItem>
        </SelectContent>
      </Select>
      <TopBettorsTable data={data} isFetching={isFetching} />
    </VStack>
  );
};

export default TopBettors;
