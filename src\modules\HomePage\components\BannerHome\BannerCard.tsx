import type { IListBannerHomeResponse } from '@/api/home-page/types';
import { Button } from '@/components/ui/button';
import { SkeletonWrapper } from '@/components/ui/skeleton-wrapper';
import { HStack, VStack } from '@/components/utilities';
import Image from 'next/image';

const BannerCard = ({ item, loading }: { item?: IListBannerHomeResponse; loading?: boolean }) => {
  return (
    <HStack
      className="flex h-[280px] rounded-2xl border border-white/15 bg-white/35 p-5 backdrop-blur-[100px]"
      spacing={24}
      noWrap
      align={'start'}
    >
      <SkeletonWrapper loading={loading}>
        <div className="w-[400px] overflow-hidden rounded-xl border border-white/35">
          <Image
            src={item?.image_url || '/images/banner-home.png'}
            width={400}
            height={236}
            alt=""
            className="h-[236px] w-full rounded-xl border border-white/35 object-cover"
          />
        </div>
      </SkeletonWrapper>
      <VStack spacing={16} className="h-full w-1/2 justify-between">
        <VStack spacing={16}>
          <SkeletonWrapper loading={loading} className="w-full">
            <p className="font-geistMono font-semibold text-tertiary-900 text-xl">{item?.title}</p>
          </SkeletonWrapper>
          <SkeletonWrapper loading={loading} className="w-full">
            <p className="line-clamp-4 font-medium text-base text-tertiary-700">{item?.description}</p>
          </SkeletonWrapper>
        </VStack>
        <SkeletonWrapper loading={loading} className="w-full">
          <Button className="bg-secondary-500" onClick={() => window.open(item?.link, '_blank')}>
            View
          </Button>
        </SkeletonWrapper>
      </VStack>
    </HStack>
  );
};

export default BannerCard;
