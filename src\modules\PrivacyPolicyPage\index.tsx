import H1 from '@/components/text/H1';
import H2 from '@/components/text/H2';
import H4 from '@/components/text/H4';
import { VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import React from 'react';

const CONFIGS = [
  {
    title: '1. Information We Collect',
    content: [
      {
        title: 'Automatically Collected Information',
        level: 2,
        descriptions: [
          'Wallet addresses and blockchain transaction data.',
          'Platform usage analytics and interaction patterns.',
          'Technical information (browser type, device information, IP address).',
          'Debate participation and performance statistics.',
        ],
      },
      {
        title: 'Information You Provide:',
        level: 2,
        descriptions: [
          ' Account preferences and settings.',
          'Communications with our support team.',
          'Feedback and user-generated content.',
        ],
      },
    ],
  },
  {
    title: '2. How We Use Your Information',
    content: [
      {
        title: 'We use collected information to:',
        level: 3,
        descriptions: [
          'Operate and improve the platform.',
          'Provide customer support.',
          'Analyze platform usage and performance.',
          'Ensure platform security and prevent abuse.',
          'Conduct research and development.',
          'Comply with legal obligations.',
        ],
      },
    ],
  },
  {
    title: '3. Information Sharing',
    content: [
      {
        title: 'We do not sell, trade, or rent your personal information. We may share information:',
        level: 3,
        descriptions: [
          'With service providers who assist in platform operations.',
          'When required by law or legal process.',
          'To protect our rights, safety, or property.',
          'In connection with a business transfer or acquisition.',
          'With your explicit consent.',
        ],
      },
    ],
  },
  {
    title: '4. Blockchain Data',
    content: [
      {
        title:
          'Since we operate on blockchain networks, certain information (wallet addresses, transaction history) is publicly accessible on the blockchain. This data cannot be deleted or modified once recorded.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '5. Cookies and Tracking',
    content: [
      {
        title: 'We use cookies and similar technologies to:',
        level: 3,
        descriptions: ['Remember your preferences.', 'Analyze platform usage.', 'Improve user experience.', 'Provide security features.'],
      },
      {
        title:
          'You can control cookie settings through your browser, though some platform features may not function properly if cookies are disabled',
        level: 3,
      },
    ],
  },
  {
    title: '6. Data Security',
    content: [
      {
        title: 'We implement reasonable security measures to protect your information, including:',
        level: 3,
        descriptions: [
          'Encryption of sensitive data.',
          'Regular security audits.',
          'Access controls and authentication.',
          'Secure data storage practices.',
        ],
      },
      {
        title: 'However, no method of transmission over the internet is 100% secure.',
        level: 3,
      },
    ],
  },
  {
    title: '7. Data Retention',
    content: [
      {
        title:
          'We retain information only as long as necessary for the purposes outlined in this policy or as required by law. Testnet data may be periodically reset or deleted.',
        level: 3,
      },
    ],
  },
  {
    title: '8. Your Rights',
    content: [
      {
        title: 'Depending on your jurisdiction, you may have rights to',
        level: 3,
        descriptions: [
          'Access your personal information.',
          'Correct inaccurate data.',
          'Request deletion of your information.',
          'Object to certain processing activities.',
          'Data portability.',
        ],
      },
    ],
  },
  {
    title: '9. International Users',
    content: [
      {
        title:
          'Our platform may be accessed globally. By using our service, you consent to the processing of your information in the Cayman Islands, which may have different privacy laws than your country.',
        level: 3,
      },
    ],
  },
  {
    title: "10. Children's Privacy",
    content: [
      {
        title:
          'Our platform is not intended for users under 18. We do not knowingly collect information from minors. If we discover we have collected information from someone under 18, we will delete it promptly.',
        level: 3,
      },
    ],
  },
  {
    title: '11. Third-Party Services',
    content: [
      {
        title:
          'Our platform may integrate with third-party services (wallets, blockchain networks, analytics providers). These services have their own privacy policies, and we are not responsible for their practices.',
        level: 3,
      },
    ],
  },
  {
    title: '12. Changes to Privacy Policy',
    content: [
      {
        title:
          'We may update this privacy policy periodically at any time for any reason. We will notify users of material changes through the platform or other communication methods.',
        level: 3,
      },
    ],
  },
  {
    title: '13. Contact Us',
    content: [
      {
        title: 'For privacy-related questions or requests, contact us at [<EMAIL>].',
        level: 3,
      },
    ],
  },
];

const PrivacyPolicyPage = () => {
  return (
    <div className="mx-auto max-w-[1063px]">
      <section className="p-6 lg:p-16">
        <H1 className="font-normal text-5xl lg:text-[68px]">Privacy policy</H1>

        <div className="my-4 font-medium text-[#00000099] text-sm uppercase lg:my-6 lg:text-base">Last Updated: 6/11/2025</div>

        <VStack spacing={32}>
          {CONFIGS?.map((item, index) => (
            <section key={index}>
              <H2 className="font-bangers font-normal text-2xl text-black lg:text-4xl">{item.title}</H2>

              {item.content?.map((contentItem, contentIndex) => (
                <div key={contentIndex}>
                  <H4
                    className={cn('my-3.5 text-[#00000099] text-sm lg:text-base', {
                      'font-semibold': contentItem.level === 2,
                      'font-medium': contentItem.level === 3,
                    })}
                  >
                    {contentItem.title}
                  </H4>
                  <ul className="list-disc space-y-0.5 pl-5 font-geistMono font-normal text-[#00000099] text-sm lg:text-base">
                    {contentItem.descriptions?.map((descriptionItem, descriptionIndex) => (
                      <li key={descriptionIndex}>{descriptionItem}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </section>
          ))}
        </VStack>
      </section>
    </div>
  );
};

export default PrivacyPolicyPage;
