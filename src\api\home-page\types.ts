import type { IPagination } from '@/types';

export interface IListBannerHomeResponse {
  id: number;
  title: string;
  image_url: string;
  link: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface IParamsLeaderboard {
  end_date?: string;
  start_date?: string;
  page: number;
  limit: number;
  sort_type?: string;
  sort_key?: string;
}

export interface ICurrentUserRankInfo {
  rank: string;
  user_id: string;
  avatar: string;
  username: string;
  wallet_address: string;
  battle_joined: string;
  bets: string;
  total_bet_amount: number;
  winner_bet_amount: number;
  total_payout: number;
}

export interface IResponseLeaderboard {
  pagination: IPagination;
  data: IDataLeaderboard[];
  current_user_data: ICurrentUserRankInfo;
}
export interface IDataLeaderboard {
  // user_id: number;
  // avatar: string;
  // username: string;
  // wallet_address: string;
  // battle_joined: number;
  // total_bet_amount: number;
  rank: number;
  user_id: number;
  avatar: string;
  username: string;
  wallet_address: string;
  battle_count: number;
  bet_count: number;
  bet_volume: number;
  total_payout: number;
}
