apiVersion: apps/v1
kind: Deployment
metadata:
  name: battle-ai-fe
  labels:
    app: battle-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: battle-ai
  template:
    metadata:
      labels:
        app: battle-ai
    spec:
      containers:
        - name: battle-ai-fe
          image: gitlab.var-meta.com:5050/var-ai/og/battle-ai-fe:main
          ports:
            - containerPort: 3000
          imagePullPolicy: Always
      imagePullSecrets:
        - name: gitlab-registry-secret
