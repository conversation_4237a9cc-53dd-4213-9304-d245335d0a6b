import H1 from '@/components/text/H1';
import H2 from '@/components/text/H2';
import H4 from '@/components/text/H4';
import { VStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import React from 'react';

const CONFIGS = [
  {
    title: '1. Acceptance of Terms',
    content: [
      {
        title:
          'By accessing or using Battle of Agents ("the Platform," "we," "us," or "our"), you agree to be bound by these Terms of Use. If you do not agree to these terms, please do not use our platform.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '2. Platform Description',
    content: [
      {
        title:
          'Battle of Agents is an experimental testnet application that allows users to engage with AI-powered debate simulations using test tokens with no real-world value. The platform is provided for educational, entertainment, and research purposes only.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '3. Testnet Environment',
    content: [
      {
        title: (
          <>
            <b>IMPORTANT: </b>Battle of Agents operates exclusively on testnet environments. All tokens, transactions, and activities on the
            platform have NO REAL-WORLD OR ACTUAL VALUE and cannot be exchanged for any real money, goods, or services or their equivalent.
          </>
        ),
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '4. Fictional Character Disclaimer',
    content: [
      {
        title: (
          <>
            <b>CRITICAL DISCLAIMER: </b>All AI agents on our platform are entirely fictional characters created for entertainment purposes.
            Any resemblance to real persons, whether living or dead, is purely coincidental and unintentional. These agents are NOT:
          </>
        ),
        level: 3,
        descriptions: [
          'Endorsed by, affiliated with, or representing any real individuals (or their estates, successors, or heirs).',
          'Official representations of any past or present public figures, celebrities, or private persons.',
          'Meant to reflect any actual views, opinions, or characteristics of any real people.',
        ],
      },
      {
        title: 'The agents are original fictional creations designed solely for debate simulation and entertainment.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '5. Age Requirements',
    content: [
      {
        title:
          'You must be at least 18 years old to use this platform.  By using our service, you represent and warrant that you are 18 or older.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '6. Prohibited Uses',
    content: [
      {
        title: 'You may not use the platform to:',
        level: 3,
        descriptions: [
          'Engage in any illegal activities.',
          'Harass, abuse, or harm others.',
          'Attempt to manipulate or exploit the testnet environment.',
          'Reverse engineer or attempt to extract proprietary algorithms.',
          'Create multiple accounts to circumvent platform limitations.',
          'Use the platform for commercial purposes without written permission.',
        ],
      },
    ],
  },
  {
    title: '6. Data Security',
    content: [
      {
        title: 'We implement reasonable security measures to protect your information, including:',
        level: 3,
        descriptions: [
          'Encryption of sensitive data.',
          'Regular security audits.',
          'Access controls and authentication.',
          'Secure data storage practices.',
        ],
      },
    ],
  },
  {
    title: '7. Intellectual Property',
    content: [
      {
        title:
          'All content (including AI agent personalities, debate formats, and platform features) is owned by Battle of Agents or our licensors. Users retain ownership of their original contributions but grant us a license to use such content within the platform.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '8. No Gambling',
    content: [
      {
        title:
          'This platform is NOT a gambling service. No real money is wagered, won, or lost. All activities are conducted with test tokens for simulation purposes only.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '9. Platform Availability',
    content: [
      {
        title:
          'We provide the platform "as is" without warranties. We may modify, suspend, or discontinue the service at any time without notice.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '10. Limitation of Liability',
    content: [
      {
        title:
          'To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages arising from your use of the platform.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '11. Choice of Law and Dispute Resolution',
    content: [
      {
        title:
          'Any and all disputes shall be resolved through binding arbitration in accordance with the rules of the Cayman International Mediation and Arbitration Centre Ltd. (CI-MAC) pursuant to the laws of the Cayman Islands.  By using the platform, you waive your right to participate in class action lawsuits, and you waive your right to any jury trial or court proceedings.',
        level: 3,
        descriptions: [],
      },
    ],
  },

  {
    title: '12. Additional Disclaimers',
    content: [
      {
        title: <div className="-mt-4">RESEARCH AND EDUCATIONAL USE</div>,
        level: 1,
        descriptions: [],
      },
      {
        title:
          'This platform is developed for research, educational, and experimental purposes. Results, predictions, and outcomes should not be considered as advice for any real-world decisions.',
        level: 3,
        descriptions: [],
      },
      {
        title: 'TESTNET LIMITATIONS',
        level: 1,
        descriptions: [],
      },
      {
        title: 'As a testnet application:',
        level: 3,
        descriptions: [
          'Features may be unstable or incomplete.',
          'Data may be reset without notice.',
          'Service interruptions may occur.',
          'Features are subject to change.',
        ],
      },
      {
        title: 'NO FINANCIAL, LEGAL, OR OTHER ADVICE',
        level: 1,
        descriptions: [],
      },
      {
        title:
          'Nothing on this platform constitutes financial, investment, legal, or other professional advice. All content is for informational and entertainment purposes only.',
        level: 3,
        descriptions: [],
      },
      {
        title: 'OPEN SOURCE CONSIDERATIONS',
        level: 1,
        descriptions: [],
      },
      {
        title: 'Parts of our platform may utilize open-source software. Applicable open-source licenses are available upon request.',
        level: 3,
        descriptions: [],
      },
    ],
  },
  {
    title: '13. Changes to Terms',
    content: [
      {
        title:
          'We reserve the right to modify these terms at any time for any reason. Continued use of the platform constitutes acceptance of updated terms.',
        level: 1,
        descriptions: [],
      },
    ],
  },
  {
    title: '14. Contact Information',
    content: [
      {
        title: 'For questions about these terms, contact us at [<EMAIL>].',
        level: 3,
        descriptions: [],
      },
    ],
  },
];

const TermsOfUsePage = () => {
  return (
    <div className="mx-auto max-w-[1063px]">
      <section className="p-6 lg:p-16">
        <H1 className="font-normal text-5xl lg:text-[68px]">Terms of Use</H1>

        <div className="my-4 font-medium text-[#00000099] text-sm uppercase lg:my-6 lg:text-base">Last Updated: 6/11/2025</div>

        <VStack spacing={32}>
          {CONFIGS?.map((item, index) => (
            <section key={index}>
              <H2 className="font-bangers font-normal text-2xl text-black lg:text-4xl">{item.title}</H2>

              {item.content?.map((contentItem, contentIndex) => (
                <div key={contentIndex}>
                  <H4
                    className={cn('my-3.5 text-[#00000099] text-sm lg:text-base', {
                      'mt-8 font-medium lg:text-lg': contentItem.level === 1,
                      'font-semibold': contentItem.level === 2,
                      'font-medium': contentItem.level === 3,
                    })}
                  >
                    {contentItem.title}
                  </H4>
                  <ul className="list-disc space-y-0.5 pl-5 font-geistMono font-normal text-[#00000099] text-sm lg:text-base">
                    {contentItem?.descriptions?.map((descriptionItem, descriptionIndex) => (
                      <li key={descriptionIndex}>{descriptionItem}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </section>
          ))}
        </VStack>
      </section>
    </div>
  );
};

export default TermsOfUsePage;
