import { HStack } from '@/components/utilities';
import { cn } from '@/libs/common';
import React from 'react';

type Props = {
  agentName?: string;
  betAmount?: React.ReactNode;
  detail?: React.ReactNode;
  detailClassName?: string;
};
const BetInfoItem = ({ agentName, betAmount, detail, detailClassName }: Props) => {
  return (
    <div className="flex flex-col gap-1 rounded-md bg-[#FFFFFF59] px-4 py-3 text-sm">
      <HStack noWrap pos="apart">
        <span className="font-semibold text-primary-700">{agentName ?? 'N/A'}</span>
        <span className="font-medium text-primary-700">Payout</span>
      </HStack>
      <HStack noWrap pos="apart">
        <span className="flex items-center font-semibold text-primary-700">{betAmount ?? 'N/A'}</span>
        <div className={cn('font-semibold text-shamrock-700', detailClassName)}>{detail ?? 'N/A'}</div>
      </HStack>
    </div>
  );
};

export default BetInfoItem;
