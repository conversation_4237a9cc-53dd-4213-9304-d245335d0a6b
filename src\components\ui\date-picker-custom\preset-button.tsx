import { cn } from '@/libs/utils';
import type { JSX } from 'react';

export const PresetButton = ({
  preset,
  label,
  isSelected,
  onClick,
}: {
  preset: string;
  label: string;
  isSelected: boolean;
  onClick: (preset: string) => void;
}): JSX.Element => {
  const handleClick = () => {
    onClick(preset);
  };

  return (
    <button
      className={cn(
        'flex h-5 items-center justify-start px-1.5 transition-colors md:h-10 md:w-full md:px-4 md:py-2 ',
        'cursor-pointer whitespace-nowrap rounded-sm bg-transparent font-medium text-gray-400 text-sm md:text-gray-400',
        'hover:bg-primary-purple hover:text-opacity-70',
        isSelected && 'pointer-events-none md:bg-primary-purple md:text-primary'
      )}
      onClick={handleClick}
    >
      {label}
    </button>
  );
};
